package com.example.audio

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log

/**
 * 系统启动接收器
 * 用于处理系统重启事件，确保应用权限设置在系统启动后仍然有效
 */
class BootReceiver : BroadcastReceiver() {
    
    companion object {
        private const val TAG = "BootReceiver"
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.action
        Log.d(TAG, "收到系统广播: $action")
        
        if (Intent.ACTION_BOOT_COMPLETED == action || 
            "android.intent.action.QUICKBOOT_POWERON" == action) {
            
            Log.d(TAG, "系统启动完成，准备执行初始化操作")
            
            try {
                // 对于华为设备，我们在系统启动后启动主页面
                // 这样可以触发应用的权限检查和请求流程
                if (isHuaweiDevice()) {
                    Log.d(TAG, "检测到华为设备，准备启动应用")
                    
                    // 延迟5秒后启动应用，避免系统启动初期资源紧张
                    Thread.sleep(5000)
                    
                    // 启动主Activity
                    val launchIntent = Intent(context, MainActivity::class.java)
                    launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(launchIntent)
                    
                    Log.d(TAG, "已发送启动主Activity的意图")
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理启动事件时出错: ${e.message}")
            }
        }
    }
    
    /**
     * 检查当前设备是否为华为设备
     */
    private fun isHuaweiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER?.toLowerCase() ?: ""
        return manufacturer.contains("huawei") || manufacturer.contains("honor")
    }
} 