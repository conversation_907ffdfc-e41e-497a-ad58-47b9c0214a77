import 'dart:async';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart' as audio_players;
import 'dart:math' as math;
import '../models/playlist_model.dart';
import '../utils/app_theme.dart';

class PlayerControls extends StatefulWidget {
  final audio_players.AudioPlayer? audioPlayer;
  final AudioFileModel? currentAudio;
  final Function() onPrevious;
  final Function() onNext;
  final Function(double) onSpeedChanged;
  final double currentSpeed;
  final Function() onPlayPause;
  final bool showOnlyUnplayedAudios;
  final Function() onToggleShowOnlyUnplayedAudios;
  final int totalAudiosCount;
  final int filteredAudiosCount;
  final Function(Duration) onSeek;

  const PlayerControls({
    Key? key,
    required this.audioPlayer,
    this.currentAudio,
    required this.onPrevious,
    required this.onNext,
    required this.onSpeedChanged,
    required this.currentSpeed,
    required this.onPlayPause,
    this.showOnlyUnplayedAudios = false,
    required this.onToggleShowOnlyUnplayedAudios,
    this.totalAudiosCount = 0,
    this.filteredAudiosCount = 0,
    required this.onSeek,
  }) : super(key: key);

  @override
  State<PlayerControls> createState() => _PlayerControlsState();
}

class _PlayerControlsState extends State<PlayerControls>
    with SingleTickerProviderStateMixin {
  final List<double> _speedOptions = [
    0.5,
    0.75,
    1.0,
    1.1,
    1.2,
    1.25,
    1.5,
    1.75,
    2.0
  ];

  // 添加动画控制器
  late AnimationController _positionController;
  bool _isInitialLoading = true;
  Duration _lastAudioPosition = Duration.zero;

  // 存储已经检查过时长差异的文件路径
  Set<String>? _checkedDurationFiles;

  @override
  void initState() {
    super.initState();
    _positionController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat(reverse: true);

    // 保存已经检查过时长差异的文件路径，避免重复日志
    _checkedDurationFiles = <String>{};

    // 监听当前文件变化，重置初始加载状态
    widget.audioPlayer?.onPositionChanged.listen((_) {
      if (mounted) {
        setState(() {
          _isInitialLoading = true;
          _lastAudioPosition = Duration.zero;
        });
      }
    });

    // 监听位置变化，在有实际播放位置后更新初始加载状态
    widget.audioPlayer?.onPositionChanged.listen((position) {
      if (_isInitialLoading && position > Duration.zero && mounted) {
        setState(() {
          _isInitialLoading = false;
        });
      }
      _lastAudioPosition = position;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 获取当前音频文件的上次保存位置（如果有）
    final initialSavedPosition =
        widget.currentAudio?.lastPositionInSeconds ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.currentAudio != null) ...[
            Text(
              widget.currentAudio!.fileName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
          ],
          StreamBuilder<Duration>(
            stream: widget.audioPlayer?.onPositionChanged,
            builder: (context, snapshot) {
              final position = snapshot.data ?? Duration.zero;

              return StreamBuilder<Duration>(
                stream: widget.audioPlayer?.onDurationChanged,
                builder: (context, snapshot) {
                  final duration = snapshot.data ?? Duration.zero;

                  // 计算最大持续时间（毫秒）
                  final maxDuration = duration.inMilliseconds.toDouble();
                  // 当前位置（毫秒）
                  final currentPosition = position.inMilliseconds.toDouble();

                  // 检查是否接近结束（当前位置 > 总时长的95%）
                  final isNearEnd = duration.inMilliseconds > 0 &&
                      position.inMilliseconds >
                          (duration.inMilliseconds * 0.95);

                  return Column(
                    children: [
                      Slider(
                        value: currentPosition < maxDuration
                            ? currentPosition
                            : maxDuration,
                        min: 0,
                        max: maxDuration > 0 ? maxDuration : 1,
                        onChanged: (value) {
                          // 用户拖动进度条时，更新初始加载状态
                          if (_isInitialLoading && mounted) {
                            setState(() {
                              _isInitialLoading = false;
                            });
                          }

                          widget.onSeek(Duration(milliseconds: value.toInt()));
                        },
                        activeColor: isNearEnd
                            ? AppTheme.completedColor
                            : AppTheme.primaryColor,
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              _formatDuration(position),
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                            Text(
                              _formatDuration(duration),
                              style: TextStyle(
                                fontSize: 12,
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              );
            },
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              StreamBuilder<dynamic>(
                  stream: widget.audioPlayer?.onPlayerStateChanged,
                  builder: (context, snapshot) {
                    // 获取播放器状态，如果不可用则默认为非加载状态
                    bool isLoading = false;
                    if (snapshot.hasData) {
                      isLoading =
                          snapshot.data == audio_players.PlayerState.stopped;
                    }

                    return IconButton(
                      onPressed: isLoading ? null : widget.onPrevious,
                      icon: const Icon(Icons.skip_previous, size: 32),
                      color: isLoading ? Colors.grey : AppTheme.primaryColor,
                    );
                  }),
              StreamBuilder<dynamic>(
                stream: widget.audioPlayer?.onPlayerStateChanged,
                builder: (context, snapshot) {
                  // 获取播放器状态，如果不可用则默认为暂停状态
                  bool isPlaying = false;
                  bool isLoading = false;
                  if (snapshot.hasData) {
                    isPlaying =
                        snapshot.data == audio_players.PlayerState.playing;
                    isLoading =
                        snapshot.data == audio_players.PlayerState.stopped;
                  }

                  if (isLoading) {
                    return Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppTheme.primaryColor,
                          ),
                        ),
                      ),
                    );
                  } else if (isPlaying) {
                    return Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.pause, size: 32),
                        color: Colors.white,
                        onPressed: widget.onPlayPause,
                      ),
                    );
                  } else {
                    return Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.play_arrow, size: 32),
                        color: Colors.white,
                        onPressed: widget.onPlayPause,
                      ),
                    );
                  }
                },
              ),
              StreamBuilder<dynamic>(
                  stream: widget.audioPlayer?.onPlayerStateChanged,
                  builder: (context, snapshot) {
                    // 获取播放器状态，如果不可用则默认为非加载状态
                    bool isLoading = false;
                    if (snapshot.hasData) {
                      isLoading =
                          snapshot.data == audio_players.PlayerState.stopped;
                    }

                    return IconButton(
                      onPressed: isLoading ? null : widget.onNext,
                      icon: const Icon(Icons.skip_next, size: 32),
                      color: isLoading ? Colors.grey : AppTheme.primaryColor,
                    );
                  }),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SizedBox(
                    height: 32,
                    child: Checkbox(
                      value: widget.showOnlyUnplayedAudios,
                      onChanged: (_) => widget.onToggleShowOnlyUnplayedAudios(),
                      activeColor: AppTheme.primaryColor,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                  const Text(
                    '隐藏已完成',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.filteredAudiosCount}/${widget.totalAudiosCount}',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  const Text(
                    '速度:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 4),
                  DropdownButton<double>(
                    value: widget.currentSpeed,
                    items: _speedOptions.map((speed) {
                      return DropdownMenuItem<double>(
                        value: speed,
                        child: Text(
                          '${speed}x',
                          style: TextStyle(
                            color: speed == widget.currentSpeed
                                ? AppTheme.primaryColor
                                : AppTheme.textPrimaryColor,
                            fontWeight: speed == widget.currentSpeed
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        widget.onSpeedChanged(value);
                      }
                    },
                    underline: Container(
                      height: 2,
                      color: AppTheme.primaryColor,
                    ),
                    isDense: true,
                    itemHeight: 48,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  // 添加dispose方法，取消所有监听
  @override
  void dispose() {
    _positionController.dispose();
    super.dispose();
  }

  // 处理时长检测的辅助方法
  bool _shouldCheckDuration(String? filePath) {
    if (filePath == null || _checkedDurationFiles == null) return false;

    if (_checkedDurationFiles!.contains(filePath)) {
      // 已经检查过，不再检查
      return false;
    }

    // 记录已检查
    _checkedDurationFiles!.add(filePath);
    return true;
  }
}
