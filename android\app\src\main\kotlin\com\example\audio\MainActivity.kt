package com.example.audio

import com.ryanheise.audioservice.AudioServiceActivity
import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivityLaunchConfigs
import io.flutter.embedding.engine.FlutterEngine
import android.view.WindowManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.IntentFilter
import android.media.AudioManager
import io.flutter.plugin.common.MethodChannel
import android.util.Log
import android.os.Build
import android.content.pm.PackageManager
import android.Manifest
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.media.AudioFocusRequest
import android.media.AudioAttributes
import android.media.AudioManager.OnAudioFocusChangeListener

class MainActivity: AudioServiceActivity() {
    private var headphoneReceiver: BroadcastReceiver? = null
    private val CHANNEL = "com.example.audio/headphone_events"
    private var methodChannel: MethodChannel? = null
    
    // 音频焦点管理相关变量
    private var audioManager: AudioManager? = null
    private var audioFocusRequest: AudioFocusRequest? = null
    private var audioFocusChangeListener: OnAudioFocusChangeListener? = null
    
    companion object {
        private const val TAG = "MainActivity"
    }
    
    // 重写 configureFlutterEngine 方法，确保引擎正确配置
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        try {
            // 初始化音频管理器
            audioManager = getSystemService(Context.AUDIO_SERVICE) as AudioManager
            
            // 设置方法通道，用于与Dart代码通信
            methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            methodChannel!!.setMethodCallHandler { call, result ->
                try {
                    when (call.method) {
                        // 提供一个方法让Flutter端检查耳机状态
                        "checkHeadphoneStatus" -> {
                            val isHeadphonesConnected = audioManager?.let {
                                it.isWiredHeadsetOn || it.isBluetoothA2dpOn || it.isBluetoothScoOn
                            } ?: false
                            result.success(isHeadphonesConnected)
                        }
                        // 注册音频焦点监听
                        "registerAudioFocusListener" -> {
                            val success = registerAudioFocusListener()
                            result.success(success)
                        }
                        else -> {
                            result.notImplemented()
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理方法调用时出错: ${e.message}")
                    result.error("ERROR", "处理方法调用时出错: ${e.message}", null)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "配置Flutter引擎时出错: ${e.message}")
        }
    }
    
    // 重写 getBackgroundMode 方法，返回透明背景模式
    override fun getBackgroundMode(): FlutterActivityLaunchConfigs.BackgroundMode {
        return FlutterActivityLaunchConfigs.BackgroundMode.transparent
    }
    
    // 重写 onCreate 方法，设置允许熄屏播放
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 允许在锁屏状态下播放
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        
        // 注册耳机拔出监听器
        registerHeadphoneDisconnectReceiver()
        
        // 对于华为设备，尝试检测系统是否刚升级
        if (isHuaweiDevice()) {
            Log.d(TAG, "检测到华为设备，检查权限状态")
            checkPermissionAfterSystemUpgrade()
        }
    }
    
    override fun onDestroy() {
        // 注销耳机拔出监听器
        unregisterHeadphoneDisconnectReceiver()
        
        // 放弃音频焦点
        abandonAudioFocus()
        
        super.onDestroy()
    }
    
    // 注册音频焦点监听器
    private fun registerAudioFocusListener(): Boolean {
        try {
            Log.d(TAG, "注册音频焦点监听器")
            
            // 检查音频管理器是否初始化
            if (audioManager == null) {
                Log.e(TAG, "音频管理器未初始化")
                return false
            }
            
            // 创建音频焦点变化监听器
            if (audioFocusChangeListener == null) {
                audioFocusChangeListener = OnAudioFocusChangeListener { focusChange ->
                    when (focusChange) {
                        AudioManager.AUDIOFOCUS_LOSS,
                        AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                            // 失去音频焦点，通知Flutter端
                            Log.d(TAG, "失去音频焦点")
                            try {
                                // 确保在主线程上调用Flutter方法
                                Handler(Looper.getMainLooper()).post {
                                    if (methodChannel != null) {
                                        methodChannel?.invokeMethod("audioFocusChanged", "lost")
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "通知Flutter音频焦点丢失失败: ${e.message}")
                            }
                        }
                        AudioManager.AUDIOFOCUS_GAIN -> {
                            // 重新获得音频焦点，通知Flutter端
                            Log.d(TAG, "重新获得音频焦点")
                            try {
                                // 确保在主线程上调用Flutter方法
                                Handler(Looper.getMainLooper()).post {
                                    if (methodChannel != null) {
                                        methodChannel?.invokeMethod("audioFocusChanged", "gained")
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "通知Flutter音频焦点获取失败: ${e.message}")
                            }
                        }
                        AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                            // 临时失去焦点但可以继续低音量播放，这里也当作失去焦点处理
                            Log.d(TAG, "临时失去音频焦点")
                            try {
                                // 确保在主线程上调用Flutter方法
                                Handler(Looper.getMainLooper()).post {
                                    if (methodChannel != null) {
                                        methodChannel?.invokeMethod("audioFocusChanged", "lost")
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "通知Flutter临时音频焦点丢失失败: ${e.message}")
                            }
                        }
                    }
                }
            }
            
            // 根据Android版本注册音频焦点监听
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0及以上版本
                val audioAttributes = AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build()
                
                audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(audioAttributes)
                    .setAcceptsDelayedFocusGain(true)
                    .setOnAudioFocusChangeListener(audioFocusChangeListener!!)
                    .build()
                
                val result = audioManager!!.requestAudioFocus(audioFocusRequest!!)
                Log.d(TAG, "Android 8.0+: 音频焦点请求结果: $result")
                return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            } else {
                // Android 8.0以下版本
                val result = audioManager!!.requestAudioFocus(
                    audioFocusChangeListener,
                    AudioManager.STREAM_MUSIC,
                    AudioManager.AUDIOFOCUS_GAIN
                )
                Log.d(TAG, "Android 8.0-: 音频焦点请求结果: $result")
                return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            }
        } catch (e: Exception) {
            Log.e(TAG, "注册音频焦点监听器失败: ${e.message}")
            return false
        }
    }
    
    // 放弃音频焦点
    private fun abandonAudioFocus() {
        try {
            if (audioManager != null && audioFocusChangeListener != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    // Android 8.0及以上版本
                    if (audioFocusRequest != null) {
                        audioManager!!.abandonAudioFocusRequest(audioFocusRequest!!)
                        Log.d(TAG, "Android 8.0+: 已放弃音频焦点")
                    }
                } else {
                    // Android 8.0以下版本
                    audioManager!!.abandonAudioFocus(audioFocusChangeListener)
                    Log.d(TAG, "Android 8.0-: 已放弃音频焦点")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "放弃音频焦点失败: ${e.message}")
        }
    }
    
    // 注册耳机拔出监听器
    private fun registerHeadphoneDisconnectReceiver() {
        try {
            headphoneReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context, intent: Intent) {
                    try {
                        if (intent.action == AudioManager.ACTION_AUDIO_BECOMING_NOISY ||
                            intent.action == Intent.ACTION_HEADSET_PLUG && intent.getIntExtra("state", -1) == 0) {
                            
                            Log.d(TAG, "检测到耳机断开事件")
                            
                            // 通过Method Channel通知Flutter端耳机已断开
                            try {
                                // 确保在主线程上调用Flutter方法
                                Handler(Looper.getMainLooper()).post {
                                    if (methodChannel != null) {
                                        methodChannel?.invokeMethod("headphoneDisconnected", null)
                                    }
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "通知Flutter耳机断开事件失败: ${e.message}")
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "处理耳机断开事件失败: ${e.message}")
                    }
                }
            }
            
            // 注册ACTION_AUDIO_BECOMING_NOISY事件监听
            val intentFilter = IntentFilter()
            intentFilter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY)
            intentFilter.addAction(Intent.ACTION_HEADSET_PLUG)
            registerReceiver(headphoneReceiver, intentFilter)
            
            Log.d(TAG, "耳机断开事件监听器注册成功")
        } catch (e: Exception) {
            Log.e(TAG, "注册耳机断开事件监听器失败: ${e.message}")
        }
    }
    
    // 注销耳机拔出监听器
    private fun unregisterHeadphoneDisconnectReceiver() {
        try {
            if (headphoneReceiver != null) {
                unregisterReceiver(headphoneReceiver)
                headphoneReceiver = null
                Log.d(TAG, "耳机断开事件监听器已注销")
            }
        } catch (e: Exception) {
            Log.e(TAG, "注销耳机断开事件监听器失败: ${e.message}")
        }
    }
    
    /**
     * 检查当前设备是否为华为设备
     */
    private fun isHuaweiDevice(): Boolean {
        val manufacturer = Build.MANUFACTURER?.toLowerCase(java.util.Locale.ROOT) ?: ""
        return manufacturer.contains("huawei") || manufacturer.contains("honor")
    }
    
    /**
     * 检查系统升级后的权限状态
     * 华为设备在系统升级后可能会重置一些权限
     */
    private fun checkPermissionAfterSystemUpgrade() {
        try {
            val handler = Handler(Looper.getMainLooper())
            
            // 延迟检查权限，确保应用完全启动
            handler.postDelayed({
                var hasPermission = false
                
                // 检查存储权限
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // Android 11+，检查管理所有文件的权限
                    hasPermission = Environment.isExternalStorageManager()
                    Log.d(TAG, "Android 11+: 管理所有文件权限状态: $hasPermission")
                } else {
                    // Android 10及以下，检查读写存储权限
                    val readPermission = checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                    val writePermission = checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                    hasPermission = readPermission && writePermission
                    Log.d(TAG, "Android 10-: 存储权限状态: $hasPermission")
                }
                
                // 如果没有权限，尝试重启应用
                if (!hasPermission) {
                    Log.d(TAG, "权限未获取，准备重启应用")
                    
                    // 发送通知到Flutter层，表明系统可能刚升级，需要重新请求权限
                    // 这部分可以通过MethodChannel实现，但这里简化处理，直接重启应用
                    
                    handler.postDelayed({
                        Log.d(TAG, "执行应用重启")
                        val intent = Intent(this, MainActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        startActivity(intent)
                    }, 1000) // 延迟1秒后重启
                }
            }, 3000) // 延迟3秒检查权限
            
        } catch (e: Exception) {
            Log.e(TAG, "检查权限状态时出错: ${e.message}")
        }
    }
}
