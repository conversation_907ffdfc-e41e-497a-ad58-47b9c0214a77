import 'dart:convert';

class PlaylistModel {
  final int? id;
  final String name;
  final List<AudioFileModel> audioFiles;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int currentPlayingIndex;
  final double playbackSpeed;

  PlaylistModel({
    this.id,
    required this.name,
    required this.audioFiles,
    required this.createdAt,
    required this.updatedAt,
    this.currentPlayingIndex = 0,
    this.playbackSpeed = 1.0,
  });

  PlaylistModel copyWith({
    int? id,
    String? name,
    List<AudioFileModel>? audioFiles,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? currentPlayingIndex,
    double? playbackSpeed,
  }) {
    return PlaylistModel(
      id: id ?? this.id,
      name: name ?? this.name,
      audioFiles: audioFiles ?? this.audioFiles,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      currentPlayingIndex: currentPlayingIndex ?? this.currentPlayingIndex,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'audioFilesJson': jsonEncode(audioFiles.map((x) => x.toMap()).toList()),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'updatedAt': updatedAt.millisecondsSinceEpoch,
      'currentPlayingIndex': currentPlayingIndex,
      'playback_speed': playbackSpeed,
    };
  }

  factory PlaylistModel.fromMap(Map<String, dynamic> map) {
    List<dynamic> audioList = [];
    try {
      // 尝试解析audioFilesJson字段
      final audioFilesJson = map['audioFilesJson'];
      if (audioFilesJson != null && audioFilesJson.isNotEmpty) {
        audioList = jsonDecode(audioFilesJson);
      }
    } catch (e) {
      print('解析播放列表JSON数据失败: $e');
      audioList = [];
    }

    return PlaylistModel(
      id: map['id'],
      name: map['name'] ?? '',
      audioFiles: List<AudioFileModel>.from(
          audioList.map((x) => AudioFileModel.fromMap(x))),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updatedAt'] ?? 0),
      currentPlayingIndex: map['currentPlayingIndex'] ?? 0,
      playbackSpeed: map['playback_speed'] != null
          ? map['playback_speed'].toDouble()
          : 1.0,
    );
  }

  @override
  String toString() {
    return 'PlaylistModel(id: $id, name: $name, audioFiles: ${audioFiles.length}, currentPlayingIndex: $currentPlayingIndex, playbackSpeed: $playbackSpeed)';
  }
}

class AudioFileModel {
  final int? id;
  final String path;
  final String fileName;
  final int durationInSeconds;
  final int lastPositionInSeconds;
  final bool isCompleted;
  final int orderIndex;

  AudioFileModel({
    this.id,
    required this.path,
    required this.fileName,
    required this.durationInSeconds,
    this.lastPositionInSeconds = 0,
    this.isCompleted = false,
    required this.orderIndex,
  });

  AudioFileModel copyWith({
    int? id,
    String? path,
    String? fileName,
    int? durationInSeconds,
    int? lastPositionInSeconds,
    bool? isCompleted,
    int? orderIndex,
  }) {
    return AudioFileModel(
      id: id ?? this.id,
      path: path ?? this.path,
      fileName: fileName ?? this.fileName,
      durationInSeconds: durationInSeconds ?? this.durationInSeconds,
      lastPositionInSeconds:
          lastPositionInSeconds ?? this.lastPositionInSeconds,
      isCompleted: isCompleted ?? this.isCompleted,
      orderIndex: orderIndex ?? this.orderIndex,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'path': path,
      'fileName': fileName,
      'durationInSeconds': durationInSeconds,
      'lastPositionInSeconds': lastPositionInSeconds,
      'isCompleted': isCompleted ? 1 : 0,
      'orderIndex': orderIndex,
    };
  }

  factory AudioFileModel.fromMap(Map<String, dynamic> map) {
    return AudioFileModel(
      id: map['id'],
      path: map['path'],
      fileName: map['fileName'],
      durationInSeconds: map['durationInSeconds'],
      lastPositionInSeconds: map['lastPositionInSeconds'],
      isCompleted: map['isCompleted'] == 1,
      orderIndex: map['orderIndex'],
    );
  }

  @override
  String toString() {
    return 'AudioFileModel(id: $id, path: $path, fileName: $fileName, durationInSeconds: $durationInSeconds, lastPositionInSeconds: $lastPositionInSeconds, isCompleted: $isCompleted, orderIndex: $orderIndex)';
  }
}
