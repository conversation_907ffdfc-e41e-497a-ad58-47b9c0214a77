# 本地音频播放器

一个功能丰富的本地音频文件播放器，支持创建和管理多个播放列表，以及调整播放速度。

## 功能特点

- 创建和管理多个播放列表
- 从文件或文件夹中选择音频文件
- 显示播放进度和完成状态
- 可调整播放顺序（拖拽排序）
- 可调整播放速度（0.5x - 2.0x）
- 自动保存播放位置和历史记录
- 美观的用户界面

## 技术栈

- Flutter SDK
- SQLite 数据库
- `just_audio` 用于音频播放
- `file_picker` 用于文件选择
- `sqflite` 用于数据库操作

## 安装运行

确保您已安装 Flutter SDK 并配置好开发环境。

1. 克隆项目
```
git clone https://github.com/your-username/audio_player.git
```

2. 安装依赖
```
flutter pub get
```

3. 运行应用
```
flutter run
```

## 权限要求

应用需要以下权限：
- 存储权限（读取本地音频文件）

## 使用说明

1. 首页点击"新建播放列表"创建播放列表
2. 输入列表名称并添加音频文件
3. 点击播放列表卡片开始播放
4. 在播放页面可以调整顺序和播放速度

## 开发者

Created by [Your Name]
