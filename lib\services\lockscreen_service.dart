import 'dart:async';
import 'package:audio_service/audio_service.dart' as audio_svc;
import 'package:flutter/foundation.dart';
import '../models/playlist_model.dart';
import 'audio_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 日志函数
void _log(String message) {
  //if (kDebugMode) {
  //  print('LockscreenService: $message');
  //}
}

class LockscreenService {
  static LockscreenService? _instance;

  // 工厂构造函数，接受AudioService实例作为参数
  factory LockscreenService(AudioService audioService) {
    _instance ??= LockscreenService._internal(audioService);
    return _instance!;
  }

  final AudioService _audioService;
  late audio_svc.AudioHandler _audioHandler;
  bool _isInitialized = false;
  Timer? _positionUpdateTimer;

  LockscreenService._internal(this._audioService);

  Future<void> init() async {
    if (_isInitialized) {
      _log('已经初始化过，跳过');
      return;
    }

    try {
      _log('初始化锁屏服务...');

      // 初始化AudioService
      _audioHandler = await audio_svc.AudioService.init(
        builder: () => MyAudioHandler(_audioService),
        config: audio_svc.AudioServiceConfig(
          androidNotificationChannelId: 'com.audio.channel.audio_playback',
          androidNotificationChannelName: '音频播放',
          androidNotificationIcon: 'mipmap/ic_launcher',
          notificationColor: const Color(0xFF2196F3),
          androidNotificationOngoing: true,
          androidStopForegroundOnPause: true,
          fastForwardInterval: const Duration(seconds: 30),
          rewindInterval: const Duration(seconds: 30),
          androidShowNotificationBadge: true,
        ),
      );

      _log('音频服务初始化成功，设置初始播放状态');

      // 初始化播放状态
      final initialState = audio_svc.PlaybackState(
        controls: [
          audio_svc.MediaControl.skipToPrevious,
          audio_svc.MediaControl.play,
          audio_svc.MediaControl.skipToNext,
          audio_svc.MediaControl.stop,
        ],
        systemActions: const {
          audio_svc.MediaAction.play,
          audio_svc.MediaAction.pause,
          audio_svc.MediaAction.skipToPrevious,
          audio_svc.MediaAction.skipToNext,
          audio_svc.MediaAction.stop,
        },
        androidCompactActionIndices: const [0, 1, 2],
        processingState: audio_svc.AudioProcessingState.ready,
        playing: false,
      );

      await (_audioHandler as MyAudioHandler).updatePlaybackState(initialState);
      _log('初始播放状态已设置');

      _isInitialized = true;
      _log('锁屏服务初始化成功');

      // 开始定期更新播放位置
      _startPositionUpdateTimer();
    } catch (e) {
      _log('初始化锁屏服务失败: $e');
      rethrow;
    }
  }

  void _startPositionUpdateTimer() {
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateMediaItem();
    });
  }

  Future<void> _updateMediaItem() async {
    if (!_isInitialized) return;

    try {
      final currentPlaylist = _audioService.currentPlaylist;
      if (currentPlaylist == null ||
          _audioService.currentIndex < 0 ||
          _audioService.currentIndex >= currentPlaylist.audioFiles.length) {
        return;
      }

      final currentAudio =
          currentPlaylist.audioFiles[_audioService.currentIndex];
      final position = _audioService.currentPosition;
      final duration = Duration(seconds: currentAudio.durationInSeconds);

      // 更新媒体项
      final mediaItem = audio_svc.MediaItem(
        id: currentAudio.path,
        title: currentAudio.fileName,
        album: currentPlaylist.name,
        duration: duration,
        artUri: null, // 可选：如果有封面图片，可以在这里设置
      );

      final state = await _audioHandler.playbackState.first;
      if (state.processingState != audio_svc.AudioProcessingState.idle) {
        await (_audioHandler as MyAudioHandler).updateMediaItem(mediaItem);
        await (_audioHandler as MyAudioHandler).updatePlaybackState(
          state.copyWith(
            updatePosition: position,
            queueIndex: _audioService.currentIndex,
          ),
        );
      }
    } catch (e) {
      _log('更新媒体项失败: $e');
    }
  }

  // 使用静态变量来限制媒体信息更新频率
  static DateTime _lastMediaInfoUpdateTime = DateTime.now();

  Future<void> updatePlaybackState({required bool isPlaying}) async {
    if (!_isInitialized) {
      _log('未初始化，无法更新播放状态');
      return;
    }

    // 避免重复更新相同状态
    final currentState = await _audioHandler.playbackState.first;
    if (currentState.playing == isPlaying) {
      // 如果状态没变，就不做任何更新
      return;
    }

    try {
      final updatedState = currentState.copyWith(
        playing: isPlaying,
        controls: [
          audio_svc.MediaControl.skipToPrevious,
          isPlaying
              ? audio_svc.MediaControl.pause
              : audio_svc.MediaControl.play,
          audio_svc.MediaControl.skipToNext,
          audio_svc.MediaControl.stop,
        ],
        systemActions: const {
          audio_svc.MediaAction.play,
          audio_svc.MediaAction.pause,
          audio_svc.MediaAction.skipToPrevious,
          audio_svc.MediaAction.skipToNext,
          audio_svc.MediaAction.stop,
        },
        androidCompactActionIndices: const [0, 1, 2],
        processingState: audio_svc.AudioProcessingState.ready,
      );

      await (_audioHandler as MyAudioHandler).updatePlaybackState(updatedState);
    } catch (e) {
      _log('更新播放状态失败: $e');
    }
  }

  Future<void> forceUpdatePlaybackState({required bool isPlaying}) async {
    if (!_isInitialized) {
      _log('未初始化，无法更新播放状态');
      return;
    }

    try {
      final currentState = await _audioHandler.playbackState.first;
      final updatedState = currentState.copyWith(
        playing: isPlaying,
        controls: [
          audio_svc.MediaControl.skipToPrevious,
          isPlaying
              ? audio_svc.MediaControl.pause
              : audio_svc.MediaControl.play,
          audio_svc.MediaControl.skipToNext,
          audio_svc.MediaControl.stop,
        ],
        systemActions: const {
          audio_svc.MediaAction.play,
          audio_svc.MediaAction.pause,
          audio_svc.MediaAction.skipToPrevious,
          audio_svc.MediaAction.skipToNext,
          audio_svc.MediaAction.stop,
        },
        androidCompactActionIndices: const [0, 1, 2],
        processingState: audio_svc.AudioProcessingState.ready,
      );

      await (_audioHandler as MyAudioHandler).updatePlaybackState(updatedState);
      _log('强制更新播放状态为: ${isPlaying ? "播放" : "暂停"}');
    } catch (e) {
      _log('强制更新播放状态失败: $e');
    }
  }

  Future<void> updateCurrentMediaInfo(
      PlaylistModel playlist, int currentIndex) async {
    if (!_isInitialized) return;

    try {
      if (currentIndex < 0 || currentIndex >= playlist.audioFiles.length) {
        // _log('无效的索引: $currentIndex');
        return;
      }

      // 添加节流逻辑，避免频繁更新
      final now = DateTime.now();
      final timeSinceLastUpdate =
          now.difference(_lastMediaInfoUpdateTime).inMilliseconds;

      // 至少间隔2000毫秒(2秒)才更新一次媒体信息
      if (timeSinceLastUpdate < 2000) {
        return;
      }

      final audioFile = playlist.audioFiles[currentIndex];

      // 更新时间戳
      _lastMediaInfoUpdateTime = now;

      final mediaItem = audio_svc.MediaItem(
        id: audioFile.path,
        title: audioFile.fileName,
        album: playlist.name,
        duration: Duration(seconds: audioFile.durationInSeconds),
        artUri: null, // 可选：如果有封面图片，可以在这里设置
      );

      await (_audioHandler as MyAudioHandler).updateMediaItem(mediaItem);

      // 更新队列
      final queue = playlist.audioFiles
          .map((audio) => audio_svc.MediaItem(
                id: audio.path,
                title: audio.fileName,
                album: playlist.name,
                duration: Duration(seconds: audio.durationInSeconds),
              ))
          .toList();

      await (_audioHandler as MyAudioHandler).updateQueue(queue);

      // 更新当前索引
      final state = await _audioHandler.playbackState.first;
      await (_audioHandler as MyAudioHandler).updatePlaybackState(
        state.copyWith(
          queueIndex: currentIndex,
        ),
      );
    } catch (e) {
      _log('更新当前媒体信息失败: $e');
    }
  }

  void dispose() {
    _positionUpdateTimer?.cancel();
    _positionUpdateTimer = null;
  }
}

// 自定义AudioHandler实现
class MyAudioHandler extends audio_svc.BaseAudioHandler {
  final AudioService _audioService;

  // 添加类级别的静态变量，用于节流日志输出
  static DateTime _lastPauseLogTime =
      DateTime.now().subtract(Duration(seconds: 10));

  // 添加类级别的静态变量，用于跟踪媒体项和播放状态变化
  static String? _lastMediaItemTitle;
  static bool? _lastPlayingState;
  static int? _lastQueueLength;

  MyAudioHandler(this._audioService) {
    _log('创建MyAudioHandler实例');
    // 初始化播放状态
    playbackState.add(audio_svc.PlaybackState(
      controls: [
        audio_svc.MediaControl.skipToPrevious,
        audio_svc.MediaControl.play,
        audio_svc.MediaControl.skipToNext,
        audio_svc.MediaControl.stop,
      ],
      systemActions: const {
        audio_svc.MediaAction.play,
        audio_svc.MediaAction.pause,
        audio_svc.MediaAction.skipToPrevious,
        audio_svc.MediaAction.skipToNext,
        audio_svc.MediaAction.stop,
      },
      androidCompactActionIndices: const [0, 1, 2],
      processingState: audio_svc.AudioProcessingState.ready,
      playing: false,
    ));

    // 设置一个默认的媒体项
    mediaItem.add(audio_svc.MediaItem(
      id: 'default',
      title: '准备播放',
      album: '音频播放器',
      duration: const Duration(seconds: 0),
    ));
  }

  // 添加便捷方法更新媒体项
  Future<void> updateMediaItem(audio_svc.MediaItem item) async {
    // 只有当标题发生变化时才打印日志
    if (_lastMediaItemTitle != item.title) {
      _log('更新媒体项: ${item.title}');
      _lastMediaItemTitle = item.title;
    }

    mediaItem.add(item);
  }

  // 添加便捷方法更新播放状态
  Future<void> updatePlaybackState(audio_svc.PlaybackState state) async {
    // 只有当播放状态发生变化时才打印日志
    if (_lastPlayingState != state.playing) {
      _log('更新播放状态: playing=${state.playing}');
      _lastPlayingState = state.playing;
    }

    playbackState.add(state);
  }

  // 添加便捷方法更新队列
  Future<void> updateQueue(List<audio_svc.MediaItem> items) async {
    // 减少日志输出频率，只在队列长度变化时记录
    if (_lastQueueLength != items.length) {
      _log('更新队列: ${items.length}个项目');
      _lastQueueLength = items.length;
    }

    queue.add(items);
  }

  @override
  Future<void> play() async {
    _log('收到锁屏播放命令');
    try {
      await _audioService.play(_audioService.currentIndex);

      // 确保媒体项存在
      if (mediaItem.value == null || mediaItem.value!.id == 'default') {
        _log('警告：没有正在播放的媒体项，尝试获取当前播放文件');
        final playlist = _audioService.currentPlaylist;
        final index = _audioService.currentIndex;

        if (playlist != null &&
            index >= 0 &&
            index < playlist.audioFiles.length) {
          final audio = playlist.audioFiles[index];
          mediaItem.add(audio_svc.MediaItem(
            id: audio.path,
            title: audio.fileName,
            album: playlist.name,
            duration: Duration(seconds: audio.durationInSeconds),
          ));
          _log('已设置当前媒体项: ${audio.fileName}');
        }
      }

      playbackState.add(playbackState.value.copyWith(
        playing: true,
        processingState: audio_svc.AudioProcessingState.ready,
        controls: [
          audio_svc.MediaControl.skipToPrevious,
          audio_svc.MediaControl.pause,
          audio_svc.MediaControl.skipToNext,
          audio_svc.MediaControl.stop,
        ],
      ));
      _log('播放命令处理完成');
    } catch (e) {
      _log('处理播放命令时出错: $e');
    }
  }

  @override
  Future<void> pause() async {
    // 避免重复输出太多日志
    final now = DateTime.now();
    final timeSinceLastLog = now.difference(_lastPauseLogTime).inSeconds;

    // 限制日志频率，至少间隔3秒才输出一次
    if (timeSinceLastLog >= 3) {
      _log('收到锁屏暂停命令');
      _lastPauseLogTime = now;
    }

    try {
      // 如果当前已经是暂停状态，则不重复调用暂停
      if (playbackState.value.playing == false) {
        return;
      }

      await _audioService.pause();

      playbackState.add(playbackState.value.copyWith(
        playing: false,
        controls: [
          audio_svc.MediaControl.skipToPrevious,
          audio_svc.MediaControl.play,
          audio_svc.MediaControl.skipToNext,
          audio_svc.MediaControl.stop,
        ],
      ));

      if (timeSinceLastLog >= 3) {
        _log('暂停命令处理完成');
      }
    } catch (e) {
      _log('处理暂停命令时出错: $e');
    }
  }

  @override
  Future<void> skipToPrevious() async {
    _log('收到锁屏上一曲命令');
    try {
      await _audioService.playPrevious();
      _log('上一曲命令处理完成');
    } catch (e) {
      _log('处理上一曲命令时出错: $e');
    }
  }

  @override
  Future<void> skipToNext() async {
    _log('收到锁屏下一曲命令');
    try {
      await _audioService.playNext();
      _log('下一曲命令处理完成');
    } catch (e) {
      _log('处理下一曲命令时出错: $e');
    }
  }

  @override
  Future<void> seek(Duration position) async {
    _log('收到锁屏定位命令: $position');
    try {
      await _audioService.seek(position);
      _log('定位命令处理完成');
    } catch (e) {
      _log('处理定位命令时出错: $e');
    }
  }

  @override
  Future<void> stop() async {
    _log('收到锁屏关闭命令');
    try {
      // 先暂停播放
      await _audioService.pause();

      // 释放资源
      await _audioService.disposeCompletely();

      // 停止前台服务
      await super.stop();

      // 完全关闭应用
      await _audioService.dispose();

      // 使用 SystemNavigator 关闭应用
      await SystemNavigator.pop();

      _log('关闭命令处理完成，应用已关闭');
    } catch (e) {
      _log('处理关闭命令时出错: $e');
    }
  }

  @override
  Future<void> close() async {
    _log('收到锁屏关闭命令');
    try {
      // 先暂停播放
      await _audioService.pause();

      // 释放资源
      await _audioService.disposeCompletely();

      // 停止前台服务
      await super.stop();

      _log('关闭命令处理完成，应用已关闭');
    } catch (e) {
      _log('处理关闭命令时出错: $e');
    }
  }
}
