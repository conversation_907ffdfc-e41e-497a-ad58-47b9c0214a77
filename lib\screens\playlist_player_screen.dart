import 'package:flutter/material.dart';
import '../models/playlist_model.dart';

import '../services/audio_service.dart';
import '../services/file_service.dart';
import '../widgets/audio_tile.dart';
import '../widgets/player_controls.dart';
import '../database/database_helper.dart';
import '../utils/app_theme.dart';
import '../widgets/alphabet_scroller.dart';
import '../main.dart'; // 导入main.dart以访问isBackgroundAudioInitialized变量
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/foundation.dart'; // 导入kDebugMode
import 'dart:async';
import 'dart:math' as math;
import 'dart:io';

// 添加日志函数，在非调试模式下不输出
void _log(String message) {
  //if (kDebugMode) {
  //  print(message);
  //}
}

class PlaylistPlayerScreen extends StatefulWidget {
  final PlaylistModel playlist;

  const PlaylistPlayerScreen({
    Key? key,
    required this.playlist,
  }) : super(key: key);

  @override
  State<PlaylistPlayerScreen> createState() => _PlaylistPlayerScreenState();
}

class _PlaylistPlayerScreenState extends State<PlaylistPlayerScreen>
    with WidgetsBindingObserver {
  final AudioService _audioService = AudioService();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final FileService _fileService = FileService();
  final ScrollController _scrollController = ScrollController();

  PlaylistModel? _currentPlaylist;
  int _currentIndex = 0;
  double _playbackSpeed = 1.0;
  bool _isLoading = false;
  bool _isInitialized = false;
  bool _hasStoragePermission = false;
  bool _isProcessingPlayback = false;
  Timer? _updateTimer;
  bool _showOnlyUnplayedAudios = true; // 默认只显示未播放的音频

  // 添加变量存储订阅
  StreamSubscription<AudioEvent>? _audioEventSubscription;

  // 字母索引相关变量
  List<String> _letters = []; // 实际显示的字母，只包含存在的字母
  final Map<String, int> _letterToIndexMap = {}; // 字母到索引的映射
  final Set<String> _existingLetters = {}; // 存在于列表中的字母

  // 更新字母索引映射和存在字母集合
  void _updateLetterIndexMap(List<AudioFileModel> audioFiles) {
    _letterToIndexMap.clear();
    _existingLetters.clear();

    for (int i = 0; i < audioFiles.length; i++) {
      final fileName = audioFiles[i].fileName;
      final firstLetter = AlphabetHelper.getFirstLetter(fileName);

      // 将字母添加到存在字母集合中
      _existingLetters.add(firstLetter);

      // 如果这个字母还没有对应的索引，则添加这个映射
      if (!_letterToIndexMap.containsKey(firstLetter)) {
        _letterToIndexMap[firstLetter] = i;
      }
    }

    // 更新显示的字母列表，只包含存在的字母
    // 首先添加向上箭头，用于滚动到顶部
    _letters = ['↑'];

    // 按字母顺序添加存在的字母
    final sortedLetters = _existingLetters.toList()
      ..sort((a, b) {
        // 特殊字符放在最后
        if (a == '#') return 1;
        if (b == '#') return -1;
        return a.compareTo(b);
      });

    // 添加所有存在的字母
    _letters.addAll(sortedLetters);

    // 打印日志，查看存在的字母
    _log('存在的字母: $_existingLetters');
    _log('显示的字母: $_letters');
  }

  // 处理字母选择
  void _onLetterSelected(String letter) {
    if (_letterToIndexMap.isEmpty || _currentPlaylist == null) return;

    if (letter == '↑') {
      // 向上箭头，滚动到顶部
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutCubic,
      );
      return;
    }

    // 如果有该字母对应的索引，滚动到该位置
    if (_letterToIndexMap.containsKey(letter)) {
      final originalIndex = _letterToIndexMap[letter]!;

      // 如果启用了过滤，需要找到过滤后列表中的对应索引
      if (_showOnlyUnplayedAudios) {
        // 获取原始文件的路径
        final originalFilePath =
            _currentPlaylist!.audioFiles[originalIndex].path;

        // 获取过滤后的文件列表
        final filteredFiles = _currentPlaylist!.audioFiles
            .where((audio) => !audio.isCompleted)
            .toList();

        // 在过滤后的列表中找到对应文件的索引
        final filteredIndex =
            filteredFiles.indexWhere((file) => file.path == originalFilePath);

        if (filteredIndex >= 0) {
          // 如果在过滤后的列表中找到了该文件，使用过滤后的索引
          _log('字母 $letter 对应的原始索引: $originalIndex, 过滤后索引: $filteredIndex');
          _scrollToIndex(filteredIndex);
        } else {
          // 如果在过滤后的列表中没有找到该文件，可能是因为该文件已经完成
          _log('字母 $letter 对应的文件在过滤后的列表中不存在');

          // 尝试找到下一个未完成的文件
          for (int i = originalIndex;
              i < _currentPlaylist!.audioFiles.length;
              i++) {
            if (!_currentPlaylist!.audioFiles[i].isCompleted) {
              final nextFilteredIndex = filteredFiles.indexWhere(
                  (file) => file.path == _currentPlaylist!.audioFiles[i].path);
              if (nextFilteredIndex >= 0) {
                _log('找到下一个未完成的文件，索引: $nextFilteredIndex');
                _scrollToIndex(nextFilteredIndex);
                return;
              }
            }
          }

          // 如果没有找到下一个未完成的文件，尝试找到上一个未完成的文件
          for (int i = originalIndex - 1; i >= 0; i--) {
            if (!_currentPlaylist!.audioFiles[i].isCompleted) {
              final prevFilteredIndex = filteredFiles.indexWhere(
                  (file) => file.path == _currentPlaylist!.audioFiles[i].path);
              if (prevFilteredIndex >= 0) {
                _log('找到上一个未完成的文件，索引: $prevFilteredIndex');
                _scrollToIndex(prevFilteredIndex);
                return;
              }
            }
          }

          // 如果还是没有找到，则滚动到列表开头
          _log('没有找到相关的未完成文件，滚动到列表开头');
          _scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOutCubic,
          );
        }
      } else {
        // 如果没有启用过滤，直接使用原始索引
        _log('字母 $letter 对应的索引: $originalIndex');
        _scrollToIndex(originalIndex);
      }
    }
  }

  // 滚动到指定索引
  void _scrollToIndex(int index) {
    if (_scrollController.hasClients &&
        index >= 0 &&
        _currentPlaylist != null) {
      try {
        // 获取可视区域高度
        final viewportHeight = _scrollController.position.viewportDimension;
        final maxScrollExtent = _scrollController.position.maxScrollExtent;

        // 获取实际显示的文件列表，考虑过滤条件
        List<AudioFileModel> displayedFiles = _currentPlaylist!.audioFiles;
        if (_showOnlyUnplayedAudios) {
          displayedFiles =
              displayedFiles.where((audio) => !audio.isCompleted).toList();
        }

        // 计算实际显示的项目总数
        final totalDisplayedItems = displayedFiles.length;

        // 如果没有显示的项目，直接返回
        if (totalDisplayedItems == 0) {
          _log('没有显示的项目，无法滚动');
          return;
        }

        // 计算总高度和平均项目高度
        final totalHeight = maxScrollExtent + viewportHeight;
        final actualItemHeight = totalHeight / totalDisplayedItems;

        // 添加日志以帮助调试
        _log(
            '滚动到字母索引: $index, 显示项数=$totalDisplayedItems, 实际项目高度=$actualItemHeight');

        // 计算目标偏移量
        double targetOffset;

        // 对于靠近列表开头的项目
        if (index < 5) {
          targetOffset =
              math.max(0, index * actualItemHeight - viewportHeight * 0.1);
          _log('滚动到列表开头项目: 索引=$index, 目标偏移=$targetOffset');
        }
        // 对于靠近列表末尾的项目
        else if (index >= totalDisplayedItems - 5) {
          targetOffset =
              math.max(0, (index * actualItemHeight) - (viewportHeight * 0.6));

          // 确保不超过最大滚动范围
          targetOffset = math.min(targetOffset, maxScrollExtent);

          _log('滚动到列表末尾项目: 索引=$index, 目标偏移=$targetOffset');
        }
        // 对于列表中间的项目
        else {
          // 将项目放在视图中间偏上的位置
          targetOffset = (index * actualItemHeight) - (viewportHeight * 0.4);

          // 确保偏移量在有效范围内
          targetOffset = math.max(0, math.min(targetOffset, maxScrollExtent));

          _log('滚动到列表中间项目: 索引=$index, 目标偏移=$targetOffset');
        }

        // 滚动到目标位置
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );

        // 二次滚动微调
        Future.delayed(const Duration(milliseconds: 350), () {
          if (_scrollController.hasClients && mounted) {
            // 获取当前实际偏移位置
            final currentOffset = _scrollController.offset;

            // 估计当前可见的第一个项目的索引
            final firstVisibleIndex =
                (currentOffset / actualItemHeight).floor();

            // 计算索引差值
            final indexDifference = index - firstVisibleIndex;

            // 如果差值表明目标不在可见区域的理想位置，进行微调
            if (indexDifference.abs() > 1 && indexDifference.abs() < 8) {
              // 使用比例因子来计算微调量，减小偏差
              final adjustFactor = indexDifference > 0 ? 0.3 : 0.7;
              final adjustedOffset = currentOffset +
                  (indexDifference * actualItemHeight * adjustFactor);

              // 确保偏移量在有效范围内
              final finalOffset = math
                  .max(0, math.min(adjustedOffset, maxScrollExtent))
                  .toDouble();

              _log('二次滚动微调: 当前首个可见项=$firstVisibleIndex, ' +
                  '偏差值=$indexDifference, 调整后=$finalOffset');

              // 执行微调滚动
              _scrollController.animateTo(
                finalOffset,
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
              );
            }
          }
        });
      } catch (e) {
        // 忽略滚动错误
        _log('滚动错误: $e');
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 确保从外部获取最新的播放列表数据，并检查权限
    _checkPermissionAndLoadData();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 当应用从后台恢复时重新初始化音频
    if (state == AppLifecycleState.resumed && _isInitialized) {
      // 应用恢复时，检查播放状态，确保界面与播放状态同步
      setState(() {
        // 更新 UI 状态
      });
      _log('播放界面：应用恢复前台，检查播放状态');

      // 确保音频服务正常工作
      _checkAudioServiceState();
    } else if (state == AppLifecycleState.paused) {
      // 应用进入后台，需要保存播放位置，这个情况下保存位置很重要
      // 因为UI定时器中不再调用saveCurrentPlayingPosition，所以需要在这里确保保存位置
      _log('播放界面：应用进入后台，主动保存播放位置');
      _saveCurrentPlayingPosition();
    }
  }

  // 检查权限并加载数据
  Future<void> _checkPermissionAndLoadData() async {
    // 先检查存储权限
    final hasPermission = await _fileService.checkStoragePermission();
    setState(() {
      _hasStoragePermission = hasPermission;
    });

    if (!hasPermission) {
      // 如果没有权限，显示提示
      if (mounted) {
        // 获取权限的友好名称
        final permissionName = _fileService.getPermissionFriendlyName();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('需要$permissionName才能播放音频文件'),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: '设置',
              onPressed: () => openAppSettings(),
            ),
          ),
        );
      }
      return;
    }

    // 有权限后，加载播放列表数据
    _loadLatestPlaylistData();
  }

  // 新增方法，从数据库和 AudioService 加载最新的播放列表数据
  Future<void> _loadLatestPlaylistData() async {
    // 检查存储权限
    if (!_hasStoragePermission) {
      final granted = await _fileService.checkStoragePermission();
      setState(() {
        _hasStoragePermission = granted;
      });

      if (!granted) {
        return; // 如果没有权限，不继续加载
      }
    }

    try {
      // 初始设置：使用传入的播放列表
      _currentPlaylist = widget.playlist;

      if (_currentPlaylist!.id != null) {
        // 始终先尝试从数据库获取最新数据，确保能看到新添加的文件
        final updatedPlaylist =
            await _databaseHelper.getPlaylistById(_currentPlaylist!.id!);
        if (updatedPlaylist != null) {
          _currentPlaylist = updatedPlaylist;

          // 从数据库加载的播放列表中获取当前播放索引
          int indexFromDb = _currentPlaylist!.currentPlayingIndex;

          // 确保索引在有效范围内
          if (indexFromDb >= 0 &&
              indexFromDb < _currentPlaylist!.audioFiles.length) {
            _currentIndex = indexFromDb;
            _log('从数据库恢复播放索引: $_currentIndex');
          } else {
            _currentIndex = 0;
            _log('数据库索引无效，使用默认索引: 0');
          }
        }

        // 如果 AudioService 已经有相同 ID 的播放列表，比较它们的 updatedAt
        // 如果 AudioService 的版本更新，则使用它（保留进度信息）
        if (_audioService.currentPlaylist != null &&
            _audioService.currentPlaylist!.id == _currentPlaylist!.id &&
            _audioService.currentPlaylist!.updatedAt
                .isAfter(_currentPlaylist!.updatedAt)) {
          // 合并播放列表：保留 AudioService 中的播放进度信息
          final updatedAudioFiles = _mergePlaylistsWithProgress(
              _currentPlaylist!.audioFiles,
              _audioService.currentPlaylist!.audioFiles);

          _currentPlaylist = _currentPlaylist!.copyWith(
            audioFiles: updatedAudioFiles,
          );

          // 使用 AudioService 中的当前索引
          _currentIndex = _audioService.currentIndex;
          _log('使用AudioService中的播放索引: $_currentIndex');
        }
      }

      // 确保索引在有效范围内
      if (_currentIndex < 0 ||
          _currentIndex >= _currentPlaylist!.audioFiles.length) {
        _currentIndex = 0;
        _log('修正为有效播放索引: $_currentIndex');
      }

      // 初始化音频播放
      _initAudio();

      // 加载完成后，检查是否所有文件都已完成，如果是，则自动取消"隐藏已完成"选项
      _updateHideCompletedState();

      // 更新字母索引映射
      _updateLetterIndexMap(_currentPlaylist!.audioFiles);
    } catch (e) {
      _log('加载最新播放列表数据失败: $e');
      // 如果发生错误，仍然尝试初始化音频
      _initAudio();
    }
  }

  // 合并两个播放列表的播放进度信息
  List<AudioFileModel> _mergePlaylistsWithProgress(
      List<AudioFileModel> latestFiles,
      List<AudioFileModel> filesWithProgress) {
    // 创建一个副本以避免修改原始列表
    final result = List<AudioFileModel>.from(latestFiles);

    // 创建一个映射，通过路径来查找有进度信息的文件
    final progressMap = <String, AudioFileModel>{};
    for (var file in filesWithProgress) {
      progressMap[file.path] = file;
    }

    // 更新每个文件的进度信息
    for (int i = 0; i < result.length; i++) {
      final path = result[i].path;
      if (progressMap.containsKey(path)) {
        // 保留原始顺序索引，但使用进度信息
        result[i] = result[i].copyWith(
          lastPositionInSeconds: progressMap[path]!.lastPositionInSeconds,
          isCompleted: progressMap[path]!.isCompleted,
        );
      }
    }

    return result;
  }

  Future<bool> _initAudio() async {
    setState(() => _isLoading = true);

    int retryCount = 0;
    const maxRetries = 3;
    bool success = false;

    while (!success && retryCount < maxRetries) {
      retryCount++;
      if (retryCount > 1) {
        _log('尝试第 $retryCount 次初始化音频播放器...');
        await Future.delayed(Duration(milliseconds: 300 * retryCount));
      }

      try {
        // 确保音频服务重新初始化
        // 如果失败并且抛出异常，我们将捕获并重试
        if (!_audioService.isInitialized) {
          _log('音频服务未初始化，尝试初始化');
          await _audioService.init();
        } else {
          _log('音频服务已初始化，检查是否需要重新加载播放列表');
        }

        // 加载播放列表并开始准备播放，但不要自动开始播放
        final loadResult = await _audioService.loadPlaylist(_currentPlaylist!);
        if (!loadResult) {
          _log('加载播放列表失败，将重试');
          continue;
        }

        // 获取最新的播放速度
        _playbackSpeed = _audioService.playbackSpeed;
        _log('从播放列表加载播放速度: $_playbackSpeed');

        // 如果当前索引有效，使用它；否则使用首个音频
        if (_currentIndex >= 0 &&
            _currentIndex < _currentPlaylist!.audioFiles.length) {
          await _audioService.prepareAtIndex(_currentIndex, autoPlay: false);
        }

        // 监听播放状态变化
        _audioService.playerStateStream.listen((state) {
          if (mounted) {
            final previousIndex = _currentIndex;
            final wasPlaying = _audioService.isPlaying;

            setState(() {
              _currentIndex = _audioService.currentIndex;
              // 确保播放状态变化后重置加载标志
              _isLoading = false;
              _isProcessingPlayback = false;
            });

            // 当播放索引改变时，滚动到当前播放的文件位置
            // 只有当索引发生变化时才强制滚动
            if (previousIndex != _currentIndex) {
              _scrollToCurrentIndex(forceScroll: true);

              // 检查前一首歌是否刚刚标记为完成（索引变化+停止播放通常意味着完成了）
              if (!wasPlaying &&
                  _currentPlaylist != null &&
                  previousIndex >= 0 &&
                  previousIndex < _currentPlaylist!.audioFiles.length &&
                  _currentPlaylist!.audioFiles[previousIndex].isCompleted) {
                // 检查是否所有文件都已完成
                _updateHideCompletedState();
              }
            }

            // 检查播放是否刚刚完成（即从播放状态变为非播放状态，通常是因为播放完成）
            if (wasPlaying &&
                !state.playing &&
                state.processingState == AudioProcessingState.completed) {
              // 检查是否所有文件都已完成
              _updateHideCompletedState();
            }
          }
        });

        // 设置播放位置变化的监听器
        _audioService.positionStream.listen((position) {
          // 更新UI上的进度显示
          if (mounted) {
            if (_currentPlaylist != null &&
                _currentPlaylist!.audioFiles.isNotEmpty &&
                _currentIndex < _currentPlaylist!.audioFiles.length) {
              // 创建一个新的音频文件列表，以便更新当前正在播放的文件的进度
              final updatedAudioFiles =
                  List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
              final currentAudioFile = updatedAudioFiles[_currentIndex];

              // 更新当前播放文件的进度
              updatedAudioFiles[_currentIndex] = currentAudioFile.copyWith(
                lastPositionInSeconds: position.inSeconds,
              );

              // 更新播放列表
              setState(() {
                _currentPlaylist = _currentPlaylist!.copyWith(
                  audioFiles: updatedAudioFiles,
                );
              });
            }
          }
        });

        // 使用AudioEventBus替代定时器，订阅音频事件来更新UI
        _audioEventSubscription =
            _audioService.audioEventBus.on<AudioEvent>().listen((event) {
          if (!mounted) return;

          _log('收到音频事件: ${event.eventType}');

          // 根据不同的事件类型更新UI
          switch (event.eventType) {
            case AudioEventType.positionChanged:
              setState(() {
                // 位置变化已由positionStream处理
              });
              break;
            case AudioEventType.playStateChanged:
              setState(() {
                // 更新播放状态
                _currentIndex = _audioService.currentIndex;

                // 滚动到当前播放的文件
                _scrollToCurrentIndex();
              });
              break;
            case AudioEventType.playbackSpeedChanged:
              // 更新UI显示当前的播放速度
              setState(() {
                // 播放速度已在AudioService中更新，触发UI刷新
                _log('收到播放速度变更事件: ${event.speed}');
              });
              break;
            case AudioEventType.durationChanged:
              setState(() {
                // 更新播放列表的文件时长
                if (event.audioFilePath != null && _currentPlaylist != null) {
                  _updateAudioFileDuration(
                      event.audioFilePath!, event.duration);
                }
              });
              break;
            case AudioEventType.playlistUpdated:
              // 播放列表更新事件 - 立即从服务中获取最新数据并强制刷新UI
              _log('收到播放列表更新事件，强制刷新播放列表');
              _forceRefreshPlaylist();
              break;
            default:
              // 默认情况下更新整个播放列表
              setState(() {
                if (_audioService.currentPlaylist != null) {
                  _currentPlaylist = _audioService.currentPlaylist;
                }
              });
          }
        });

        _isInitialized = true;

        // 初始化完成后，滚动到当前播放的文件位置
        // 等待界面构建完成后执行滚动
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToCurrentIndex(forceScroll: true);
        });

        success = true;
      } catch (e) {
        _log('初始化音频播放器失败 (尝试 $retryCount/$maxRetries): $e');
        if (retryCount >= maxRetries && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('初始化播放器失败: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
            _isProcessingPlayback = false;
          });
        }
      }
    }

    return success;
  }

  // 滚动到当前播放的文件位置
  void _scrollToCurrentIndex({bool forceScroll = false}) {
    if (_currentIndex < 0 ||
        _currentPlaylist == null ||
        _currentPlaylist!.audioFiles.isEmpty ||
        _currentIndex >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    // 确保列表已经构建
    if (_scrollController.hasClients) {
      try {
        // 在过滤模式下，需要找到当前播放文件在过滤列表中的索引
        int indexToScroll = _currentIndex;
        if (_showOnlyUnplayedAudios) {
          final currentAudioPath =
              _currentPlaylist!.audioFiles[_currentIndex].path;
          // 首先检查当前播放的文件是否在过滤后的列表中
          List<AudioFileModel> filteredFiles = _currentPlaylist!.audioFiles
              .where((audio) => !audio.isCompleted)
              .toList();

          int filteredIndex =
              filteredFiles.indexWhere((file) => file.path == currentAudioPath);
          if (filteredIndex < 0) {
            // 当前播放的文件不在过滤后的列表中，无需滚动
            return;
          }
          indexToScroll = filteredIndex;
        }

        // 获取可视区域高度
        final viewportHeight = _scrollController.position.viewportDimension;
        final maxScrollExtent = _scrollController.position.maxScrollExtent;
        final currentOffset = _scrollController.offset;

        // 获取实际显示的文件列表，考虑过滤条件
        List<AudioFileModel> displayedFiles = _currentPlaylist!.audioFiles;
        if (_showOnlyUnplayedAudios) {
          displayedFiles =
              displayedFiles.where((audio) => !audio.isCompleted).toList();
        }

        // 计算实际的项目高度，基于列表总高度和项目数量
        // 这种计算方式比固定估计值更准确
        final totalDisplayedItems = displayedFiles.length;
        final totalHeight = maxScrollExtent + viewportHeight;
        final actualItemHeight =
            totalDisplayedItems > 0 ? totalHeight / totalDisplayedItems : 0;

        // 记录日志用于调试
        _log(
            '滚动计算: 总项目数=$totalDisplayedItems, 总高度=$totalHeight, 实际项目高度=$actualItemHeight');

        // 估算当前项的位置范围
        final estimatedItemTop = indexToScroll * actualItemHeight;
        final estimatedItemBottom = estimatedItemTop + actualItemHeight;

        // 检查当前项是否已经在可视区域内
        final viewportTop = currentOffset;
        final viewportBottom = currentOffset + viewportHeight;

        // 如果已经在可视区域内且不是强制滚动，则不执行滚动
        if (!forceScroll &&
            estimatedItemTop >= viewportTop &&
            estimatedItemBottom <= viewportBottom) {
          return;
        }

        // 使用这种滚动方式，确保项目出现在可视区域中央
        // 先计算项目应该滚动到的位置
        double targetOffset;

        // 添加日志以帮助调试
        _log(
            '滚动到播放项: 索引=$indexToScroll, 总项数=${displayedFiles.length}, 当前偏移=$currentOffset, 可视区域高度=$viewportHeight');

        // 这种算法在列表很长时效果更好
        if (indexToScroll < 5) {
          // 对于靠近列表开头的项目，滚动到列表开头
          targetOffset = 0;
          _log('滚动到列表开头: 索引=$indexToScroll, 目标偏移=$targetOffset');
        } else if (indexToScroll > totalDisplayedItems - 5) {
          // 对于靠近列表末尾的项目，确保能看到列表末尾
          // 但仍然将当前项放在视图的合适位置
          targetOffset = math.max(0, maxScrollExtent - viewportHeight / 2);
          _log('滚动到列表末尾: 索引=$indexToScroll, 目标偏移=$targetOffset');
        } else {
          // 对于列表中间的项目，精确计算位置使其居中显示
          // 使用比例定位，让当前项始终在视图的40%位置（略微偏上）
          targetOffset = estimatedItemTop - (viewportHeight * 0.4);

          // 确保不会越界
          targetOffset = math.max(0, math.min(targetOffset, maxScrollExtent));

          _log(
              '滚动到列表中间: 索引=$indexToScroll, 估计顶部位置=$estimatedItemTop, 目标偏移=$targetOffset');
        }

        // 使用精确的滚动动画
        _scrollController.animateTo(
          targetOffset,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );

        // 添加二次滚动，在第一次滚动后细微调整，确保精确定位
        Future.delayed(const Duration(milliseconds: 350), () {
          if (_scrollController.hasClients && mounted) {
            // 获取当前可见的第一个项目的估计索引
            final firstVisibleIndex =
                (_scrollController.offset / actualItemHeight).floor();

            // 计算当前项与第一个可见项的偏移
            final offset = indexToScroll - firstVisibleIndex;

            // 如果偏移超过阈值，进行微调
            if (offset.abs() > 2 && offset.abs() < 8) {
              // 根据偏移量计算微调位置
              final offsetFactor = offset > 0 ? 0.6 : 0.4; // 调整系数，减少偏差
              final adjustedOffset = _scrollController.offset +
                  (offset * actualItemHeight * offsetFactor);

              // 确保不会越界
              final finalOffset = math
                  .max(0, math.min(adjustedOffset, maxScrollExtent))
                  .toDouble();

              _log(
                  '二次滚动微调: 首个可见项索引=$firstVisibleIndex, 偏移量=$offset, 调整后偏移=$finalOffset');

              _scrollController.animateTo(
                finalOffset,
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeOut,
              );
            }
          }
        });
      } catch (e) {
        // 忽略滚动错误
        _log('滚动错误: $e');
      }
    }
  }

  // 强制刷新播放列表状态
  void _forceRefreshPlaylist() {
    if (!mounted) return;
    if (_audioService.currentPlaylist != null) {
      setState(() {
        _currentPlaylist = _audioService.currentPlaylist;
        _currentIndex = _audioService.currentIndex;
        // 同时更新播放速度显示
        _playbackSpeed = _audioService.playbackSpeed;
        _log('强制刷新播放列表，当前速度: $_playbackSpeed');
      });

      // 延迟滚动确保列表已经重建
      Future.delayed(Duration(milliseconds: 100), () {
        _scrollToCurrentIndex(forceScroll: true);
      });
    }
  }

  // 添加一个方法保存当前播放文件的进度
  Future<void> _saveCurrentPlayingPosition() async {
    try {
      // 使用AudioService的公开方法，它会直接更新数据库和内存中的模型
      await _audioService.saveCurrentAudioPosition();

      // 更新UI
      if (mounted && _audioService.currentPlaylist != null) {
        setState(() {
          _currentPlaylist = _audioService.currentPlaylist;
        });
      }
    } catch (e) {
      // 静默处理错误，因为这是后台操作
      _log('保存播放位置失败: $e');
    }
  }

  Future<void> _playAudioAtIndex(int index) async {
    if (_isProcessingPlayback) return; // 防止重复点击

    setState(() {
      _isProcessingPlayback = true;
      _isLoading = true;
    });

    try {
      if (index >= 0 && index < _currentPlaylist!.audioFiles.length) {
        // 如果启用了隐藏已完成音频的过滤功能，并且点击的是已完成的音频
        // 我们需要先检查这个音频文件是否已完成
        AudioFileModel audioFile = _currentPlaylist!.audioFiles[index];

        // 获取当前播放器是否正在播放
        final wasPlaying = _audioService.isPlaying;
        _log('当前播放器状态: ${wasPlaying ? "播放中" : "已暂停"}');

        // 新增：检查当前是否正在播放且点击的是已完成的文件
        if (wasPlaying && audioFile.isCompleted) {
          _log('在播放状态下点击已完成的文件，自动暂停播放');
          await _audioService.saveCurrentAudioPosition(); // 保存当前播放位置
          await _audioService.pause(); // 暂停播放

          // 更新当前索引，以便在用户再次点击播放按钮时从该文件开始播放
          setState(() {
            _currentIndex = index;
          });

          // 同步更新 AudioService 中的播放列表
          await _audioService.loadPlaylist(_currentPlaylist!,
              startIndex: _currentIndex);

          // 通知用户
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('已暂停播放。若要重播"${audioFile.fileName}"，请点击播放按钮'),
                duration: const Duration(seconds: 2),
              ),
            );
          }

          // 滚动到当前索引
          _scrollToCurrentIndex();
          return; // 直接返回，不执行后续代码
        }

        if (_showOnlyUnplayedAudios &&
            audioFile.isCompleted &&
            _currentPlaylist!.audioFiles.any((file) => !file.isCompleted)) {
          _log('尝试播放已完成的音频，但启用了隐藏已完成音频的功能');
          _log('查找下一个未完成的音频文件...');

          // 查找下一个未完成的音频文件
          int newIndex = -1;
          for (int i = 0; i < _currentPlaylist!.audioFiles.length; i++) {
            if (!_currentPlaylist!.audioFiles[i].isCompleted) {
              newIndex = i;
              break;
            }
          }

          if (newIndex != -1) {
            _log('找到未完成的音频，改为播放索引 $newIndex');
            index = newIndex;
          } else {
            _log('没有找到未完成的音频，继续播放选定的文件');
          }
        }

        // 确保文件存在
        final file = File(_currentPlaylist!.audioFiles[index].path);
        if (!file.existsSync()) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('文件不存在，无法播放')),
            );
          }
          return;
        }

        _log(
            '准备播放文件索引 $index: ${_currentPlaylist!.audioFiles[index].fileName}');

        // 添加额外日志，帮助诊断问题
        _log('当前是否正在播放: ${_audioService.isPlaying}');

        // 如果播放器没有初始化，先初始化
        if (!_isInitialized) {
          _log('播放器未初始化，先初始化');
          await _initAudio();
        }

        try {
          // 先尝试停止当前播放，确保清除状态
          if (_audioService.isPlaying) {
            await _audioService.pause();
            // 短暂延迟让播放器状态更新
            await Future.delayed(const Duration(milliseconds: 100));
          }

          // 先更新当前索引，确保UI反映正确的选中状态
          setState(() {
            _currentIndex = index;
          });

          // 同步更新 AudioService 中的播放列表
          await _audioService.loadPlaylist(_currentPlaylist!,
              startIndex: _currentIndex);

          // 使用 try-catch 包裹播放操作
          // 只有在之前播放器状态为播放中时，才自动开始播放
          if (wasPlaying) {
            // 在开始播放前检查是否需要重置状态
            audioFile = _currentPlaylist!.audioFiles[index];
            if (audioFile.isCompleted) {
              _log('准备播放的文件是已完成状态，自动将其状态修改为未完成并重置播放位置');

              try {
                // 使用AudioService中的新方法标记为未完成
                await _audioService.markAudioFileAsUnfinished(index);
                _log('音频文件已标记为未完成状态');

                // 更新当前播放列表（从AudioService获取最新状态）
                setState(() {
                  _currentPlaylist = _audioService.currentPlaylist;
                });

                // 通知用户
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('已将"${audioFile.fileName}"标记为未听完并从头开始播放'),
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }

                // 强制从头开始播放，确保seek位置为0
                try {
                  _log('从头开始播放索引 $index');
                  await _audioService.seek(Duration.zero); // 确保从0开始播放
                  await _audioService.play(index);
                  _log('成功从头启动播放索引 $index');

                  _scrollToCurrentIndex();
                  return; // 播放已开始，不需要执行后面的代码
                } catch (e) {
                  _log('从头播放失败: $e');
                  // 如果失败，继续尝试常规播放方式
                }
              } catch (e) {
                _log('标记音频为未完成状态失败: $e');
                // 继续使用常规播放方式
              }
            }

            try {
              _log('开始播放索引 $index');
              await _audioService.play(index);
              _log('成功启动播放索引 $index');

              _scrollToCurrentIndex();
            } catch (e) {
              _log('播放失败，尝试完全重启播放器: $e');

              // 重启播放器并再次尝试
              await _restartPlayback();

              // 延迟一下再次尝试播放
              await Future.delayed(const Duration(milliseconds: 500));

              try {
                _log('重启后再次尝试播放索引 $index');
                await _audioService.play(index);

                _scrollToCurrentIndex();
                _log('重启后播放成功');
              } catch (retryError) {
                _log('重启后播放仍然失败: $retryError');

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('播放失败，请检查音频文件或重启应用'),
                      duration: const Duration(seconds: 3),
                    ),
                  );
                }
              }
            }
          } else {
            // 如果之前是暂停状态，只准备音频但不开始播放
            _log('当前为暂停状态，只切换到选定的文件但不开始播放');
            await _audioService.prepareAtIndex(index, autoPlay: false);
            _scrollToCurrentIndex();
          }
        } catch (e) {
          _log('播放操作过程中出错: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('播放出错: $e')),
            );
          }
        }
      }
    } catch (e) {
      _log('播放音频完全失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('无法播放音频: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPlayback = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _playNext() async {
    if (_isProcessingPlayback) return; // 防止重复点击

    setState(() {
      _isProcessingPlayback = true;
      _isLoading = true;
    });

    try {
      _log('开始播放下一首...');
      // 记录当前播放状态
      bool wasPlaying = _audioService.isPlaying;
      _log('切换前的播放状态: ${wasPlaying ? "播放中" : "已暂停"}');

      // 使用AudioService中的playNext方法，它会考虑已完成的音频
      // playNext方法内部已经保存了播放状态，不需要在这里暂停
      await _audioService.playNext();

      // 使用_audioService中的currentIndex更新当前索引
      setState(() {
        _currentIndex = _audioService.currentIndex;
      });

      // 滚动到新的当前播放项
      _scrollToCurrentIndex(forceScroll: true);

      // 检查是否所有文件都已完成，如果是，则自动取消"隐藏已完成"选项
      _updateHideCompletedState();

      _log('成功切换到下一首，当前索引: ${_audioService.currentIndex}');
    } catch (e) {
      _log('播放下一首失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放下一首失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPlayback = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _playPrevious() async {
    if (_isProcessingPlayback) return; // 防止重复点击

    setState(() {
      _isProcessingPlayback = true;
      _isLoading = true;
    });

    try {
      _log('开始播放上一首...');
      // 记录当前播放状态
      bool wasPlaying = _audioService.isPlaying;
      _log('切换前的播放状态: ${wasPlaying ? "播放中" : "已暂停"}');

      // 使用AudioService中的playPrevious方法，它会考虑已完成的音频
      // playPrevious方法内部已经保存了播放状态，不需要在这里暂停
      await _audioService.playPrevious();

      // 使用_audioService中的currentIndex更新当前索引
      setState(() {
        _currentIndex = _audioService.currentIndex;
      });

      // 滚动到新的当前播放项
      _scrollToCurrentIndex(forceScroll: true);

      // 检查是否所有文件都已完成，如果是，则自动取消"隐藏已完成"选项
      _updateHideCompletedState();

      _log('成功切换到上一首，当前索引: ${_audioService.currentIndex}');
    } catch (e) {
      _log('播放上一首失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放上一首失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingPlayback = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _togglePlayPause() async {
    if (_isProcessingPlayback) return; // 防止重复点击

    setState(() {
      _isProcessingPlayback = true;
      _isLoading = true;
    });

    try {
      _log('当前播放状态: ${_audioService.isPlaying ? '播放中' : '已暂停'}');

      // 确保播放器初始化
      if (!_isInitialized) {
        _log('播放器未初始化，先初始化');
        await _initAudio();
      }

      if (_audioService.isPlaying) {
        // 如果正在播放，先保存当前位置再暂停
        try {
          _log('正在暂停播放...');
          await _audioService.saveCurrentAudioPosition();
          await _audioService.pause();
          _log('暂停成功');
        } catch (e) {
          _log('暂停失败: $e');
          // 即使暂停失败，也继续尝试后续操作
        }
      } else {
        // 如果当前没有播放，尝试播放
        try {
          _log('尝试开始播放...');

          // 检查当前文件是否是已完成状态，如果是，先将其状态修改为未完成并重置播放位置
          if (_currentIndex >= 0 &&
              _currentIndex < _currentPlaylist!.audioFiles.length &&
              _currentPlaylist!.audioFiles[_currentIndex].isCompleted) {
            _log('准备播放的文件是已完成状态，自动将其状态修改为未完成并重置播放位置');

            try {
              // 使用AudioService中的新方法标记为未完成
              await _audioService.markAudioFileAsUnfinished(_currentIndex);
              _log('音频文件已标记为未完成状态');

              // 更新当前播放列表（从AudioService获取最新状态）
              setState(() {
                _currentPlaylist = _audioService.currentPlaylist;
              });

              // 直接开始播放，跳过下面的位置恢复代码
              await _audioService.play(_currentIndex);
              _log('播放成功');
              return;
            } catch (e) {
              _log('标记音频为未完成状态失败: $e');
              // 继续尝试常规的播放方式
            }
          }

          // 检查当前位置是否为 0，如果是，尝试恢复到上次的位置
          final currentPosition = _audioService.currentPosition;
          _log('当前位置: ${currentPosition.inSeconds} 秒');

          if (currentPosition.inSeconds == 0 &&
              _currentIndex < _currentPlaylist!.audioFiles.length) {
            final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
            if (audioFile.lastPositionInSeconds > 0) {
              try {
                _log('尝试恢复到上次位置: ${audioFile.lastPositionInSeconds} 秒');
                await _audioService
                    .seek(Duration(seconds: audioFile.lastPositionInSeconds));
              } catch (seekError) {
                _log('恢复位置失败: $seekError');
              }
            }
          }

          // 开始播放
          await _audioService.play(_currentIndex);
          _log('播放成功');
        } catch (e) {
          _log('开始播放失败: $e');

          // 尝试彻底重启播放器
          try {
            _log('尝试重启播放器...');
            await _restartPlayback();

            // 延迟后再次尝试播放
            await Future.delayed(const Duration(milliseconds: 500));

            if (_currentIndex >= 0 &&
                _currentIndex < _currentPlaylist!.audioFiles.length) {
              _log('重启后尝试播放当前索引: $_currentIndex');
              await _audioService.play(_currentIndex);
              _log('重启后播放成功');
            }
          } catch (restartError) {
            _log('重启播放器失败: $restartError');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('播放失败，请尝试重启应用'),
                  duration: Duration(seconds: 3),
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      _log('切换播放/暂停失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放控制失败: $e')),
        );
      }
    } finally {
      // 确保 UI 状态与实际播放状态一致
      if (mounted) {
        setState(() {
          _isProcessingPlayback = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setPlaybackSpeed(double speed) async {
    try {
      if (!_isInitialized) {
        await _initAudio();
      }

      await _audioService.setPlaybackSpeed(speed);

      setState(() {
        _playbackSpeed = speed;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('设置播放速度失败: $e')),
        );
      }
    }
  }

  Future<void> _reorderAudioFiles(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }

    // 如果已经在处理操作，则直接返回，防止重复操作
    if (_isProcessingPlayback) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isProcessingPlayback = true;
    });

    try {
      await _audioService.reorderAudioFiles(oldIndex, newIndex);

      // 更新当前播放列表
      setState(() {
        _currentPlaylist = _audioService.currentPlaylist;
        _currentIndex = _audioService.currentIndex;
      });

      // 添加一个短暂延迟，确保界面更新
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('重新排序失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isProcessingPlayback = false;
        });
      }
    }
  }

  // 重新启动播放，确保播放器能正确响应
  Future<void> _restartPlayback() async {
    // 如果已经在处理操作，则直接返回，防止重复操作
    if (_isProcessingPlayback) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isProcessingPlayback = true;
    });

    try {
      _log('开始完全重启播放器...');

      // 首先保存当前位置
      await _audioService.saveCurrentAudioPosition();

      // 停止当前播放
      try {
        await _audioService.stop();
        _log('成功停止播放');
      } catch (stopError) {
        _log('停止播放失败，但会继续重置: $stopError');
      }

      // 完全重置播放器状态，包括后台播放标志
      try {
        // 在 main.dart 中重置后台播放状态
        _log('重置全局后台播放状态');
        isBackgroundAudioInitialized = false;

        // 短暂延迟，让系统有时间释放资源
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (resetError) {
        _log('重置状态出错: $resetError');
      }

      // 强制重新完整初始化音频服务
      _log('重新初始化音频系统');
      _isInitialized = false;

      // 重新初始化音频服务
      await _audioService.init();

      // 重新加载当前播放列表
      if (_currentPlaylist != null) {
        await _audioService.loadPlaylist(_currentPlaylist!,
            startIndex: _currentIndex);
      }

      // 如果需要继续播放，准备当前索引
      _log('准备当前音频');
      if (_currentIndex >= 0 &&
          _currentPlaylist != null &&
          _currentPlaylist!.audioFiles.isNotEmpty &&
          _currentIndex < _currentPlaylist!.audioFiles.length) {
        await _audioService.prepareAtIndex(_currentIndex, autoPlay: false);
      }

      _log('播放器重启完成');

      // 通知用户
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('播放器已完全重启')),
        );
      }
    } catch (e) {
      _log('重新启动播放器失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('重新启动播放器失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isProcessingPlayback = false;
        });
      }
    }
  }

  // 从数据库重新加载最新的播放列表数据
  Future<void> _reloadPlaylistFromDatabase() async {
    if (_currentPlaylist?.id == null) return;

    // 如果已经在处理操作，则直接返回，防止重复操作
    if (_isProcessingPlayback) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isProcessingPlayback = true;
    });

    try {
      final updatedPlaylist =
          await _databaseHelper.getPlaylistById(_currentPlaylist!.id!);
      if (updatedPlaylist != null) {
        // 保存当前播放位置
        await _audioService.saveCurrentAudioPosition();

        // 更新播放列表
        setState(() {
          _currentPlaylist = updatedPlaylist;
        });

        // 重新初始化音频播放
        await _audioService.stop();
        await _initAudio();

        // 添加一个短暂延迟，确保界面更新
        await Future.delayed(const Duration(milliseconds: 200));

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('已刷新播放列表，新增文件已显示')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('刷新播放列表失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isProcessingPlayback = false;
        });
      }
    }
  }

  // 切换是否只显示未播放的音频文件
  void _toggleShowOnlyUnplayedAudios() {
    setState(() {
      _showOnlyUnplayedAudios = !_showOnlyUnplayedAudios;
    });
  }

  // 检查是否所有文件都已完成
  bool _checkAllFilesCompleted() {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      return false;
    }

    // 检查是否所有文件都已标记为完成
    return _currentPlaylist!.audioFiles.every((file) => file.isCompleted);
  }

  // 更新"隐藏已完成"选项状态 - 如果所有文件都已完成，自动取消勾选
  void _updateHideCompletedState() {
    if (_showOnlyUnplayedAudios && _checkAllFilesCompleted()) {
      setState(() {
        _showOnlyUnplayedAudios = false;
      });

      // 显示提示，告知用户已自动显示所有文件
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('所有文件已完成，已自动显示全部文件'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 添加方法：切换音频文件的完成状态
  Future<void> _toggleAudioCompleteStatus(int index) async {
    if (index < 0 ||
        _currentPlaylist == null ||
        index >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    try {
      final audioFile = _currentPlaylist!.audioFiles[index];
      final newStatus = !audioFile.isCompleted;

      // 创建对话框确认操作
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(newStatus ? '标记为已听完' : '标记为未听完'),
          content: Text(
              '确定将"${audioFile.fileName}"标记为${newStatus ? '已听完' : '未听完'}吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      setState(() {
        _isLoading = true;
      });

      // 创建更新后的音频文件
      final updatedAudioFile = audioFile.copyWith(
        isCompleted: newStatus,
        // 如果标记为已完成，将进度设置为文件总时长
        // 如果标记为未完成，只有在非当前播放文件时才重置播放位置为0
        lastPositionInSeconds: newStatus
            ? (audioFile.durationInSeconds > 0
                ? audioFile.durationInSeconds
                : audioFile.lastPositionInSeconds)
            : (index == _currentIndex && _audioService.isPlaying
                ? audioFile.lastPositionInSeconds // 如果是当前正在播放的文件，保留当前位置
                : 0), // 其他情况，当标记为未听完时，重置播放位置为0
      );

      // 创建更新后的音频文件列表
      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      updatedAudioFiles[index] = updatedAudioFile;

      // 更新当前播放列表
      final updatedPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
        updatedAt: DateTime.now(),
      );

      // 更新状态
      setState(() {
        _currentPlaylist = updatedPlaylist;
      });

      // 更新音频服务中的播放列表
      if (_currentPlaylist!.id != null) {
        // 记录当前的播放状态
        final wasPlaying = _audioService.isPlaying;
        _log('保存播放状态：${wasPlaying ? "正在播放" : "已暂停"}');

        await _databaseHelper.updatePlaylist(_currentPlaylist!);

        // 同时更新播放历史记录，将位置重置为0
        await _databaseHelper.updateAudioFilePosition(audioFile.path, 0,
            playlistId: _currentPlaylist!.id);
        _log('播放历史记录已更新，位置重置为0');

        // 同步更新 AudioService 中的播放列表
        await _audioService.loadPlaylist(_currentPlaylist!,
            startIndex: _currentIndex);

        // 恢复播放状态 - 如果之前在播放且是当前播放的文件被修改，确保继续播放
        if (wasPlaying && index == _currentIndex) {
          _log('恢复播放状态');
          await _audioService.play(_currentIndex);
        }
      }

      // 显示操作成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '已将"${audioFile.fileName}"标记为${newStatus ? '已听完' : '未听完'}${!newStatus && index != _currentIndex ? '并重置播放进度' : ''}'),
            duration: const Duration(seconds: 2),
          ),
        );

        // 如果当前过滤模式为仅显示未完成，且将文件标记为已完成，则刷新UI
        if (_showOnlyUnplayedAudios && newStatus) {
          setState(() {
            // 触发UI刷新
          });
        }

        // 检查是否所有文件都已完成，如果是，则自动取消"隐藏已完成"选项
        if (newStatus) {
          _updateHideCompletedState();
        }
      }
    } catch (e) {
      _log('切换音频完成状态失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 添加方法更新音频文件的时长
  void _updateAudioFileDuration(String filePath, Duration? duration) {
    if (duration == null || _currentPlaylist == null) return;

    final updatedAudioFiles =
        List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
    final index = updatedAudioFiles.indexWhere((file) => file.path == filePath);

    if (index != -1) {
      updatedAudioFiles[index] = updatedAudioFiles[index].copyWith(
        durationInSeconds: duration.inSeconds,
      );

      _currentPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    List<AudioFileModel> audioFiles = _currentPlaylist?.audioFiles ?? [];

    // 如果需要过滤，则只显示未完成的音频文件（包括未播放和部分播放的）
    if (_showOnlyUnplayedAudios && audioFiles.isNotEmpty) {
      audioFiles = audioFiles.where((audio) {
        // 过滤掉已完成的音频文件，保留未完成的
        return !audio.isCompleted;
      }).toList();
    }

    final currentAudio =
        _currentIndex < (_currentPlaylist?.audioFiles.length ?? 0)
            ? _currentPlaylist?.audioFiles[_currentIndex]
            : null;

    // 如果没有存储权限，显示权限请求界面
    if (!_hasStoragePermission) {
      // 获取权限的友好名称
      final permissionName = _fileService.getPermissionFriendlyName();

      return Scaffold(
        appBar: AppBar(
          title: const Text('播放列表'),
          centerTitle: true,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.folder_off,
                size: 72,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                '需要$permissionName才能播放音频文件',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () async {
                  final granted = await _fileService.checkStoragePermission();
                  setState(() {
                    _hasStoragePermission = granted;
                  });

                  if (granted) {
                    _loadLatestPlaylistData();
                  } else {
                    // 再次尝试获取权限失败，提示用户去设置页面手动开启
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('请在设置中手动授予$permissionName'),
                          action: SnackBarAction(
                            label: '设置',
                            onPressed: () => openAppSettings(),
                          ),
                        ),
                      );
                    }
                  }
                },
                child: const Text('请求权限'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('返回'),
              ),
            ],
          ),
        ),
      );
    }

    // 使用WillPopScope包装Scaffold，拦截返回按钮事件
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          title: Text(_currentPlaylist?.name ?? '播放列表'),
          centerTitle: true,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () async {
              // 自定义返回按钮，确保在返回前清理资源
              await _cleanupResourcesBeforeExit();
              if (mounted) {
                Navigator.of(context).pop();
              }
            },
          ),
          actions: [
            // 添加更多选项的刷新菜单
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                if (value == 'restart') {
                  _restartPlayback();
                } else if (value == 'reload') {
                  _reloadPlaylistFromDatabase();
                } else if (value == 'settings') {
                  openAppSettings(); // 添加打开设置选项
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem<String>(
                  value: 'restart',
                  child: ListTile(
                    leading: Icon(Icons.refresh),
                    title: Text('重启播放器'),
                    subtitle: Text('修复播放卡住的问题'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'reload',
                  child: ListTile(
                    leading: Icon(Icons.sync),
                    title: Text('刷新播放列表'),
                    subtitle: Text('加载新添加的文件'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'settings',
                  child: ListTile(
                    leading: const Icon(Icons.settings),
                    title: const Text('权限设置'),
                    subtitle:
                        Text('管理${_fileService.getPermissionFriendlyName()}'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                // 添加操作提示
                const PopupMenuItem<String>(
                  value: 'hint',
                  enabled: false,
                  child: ListTile(
                    leading: Icon(Icons.touch_app),
                    title: Text('提示'),
                    subtitle: Text('长按音频可标记为已听完/未听完'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
        body: audioFiles.isEmpty
            ? const Center(child: Text('播放列表为空'))
            : Stack(
                children: [
                  // 始终显示播放列表
                  Column(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            // 字母索引小部件
                            AlphabetScroller(
                              letters: _letters,
                              onLetterSelected: _onLetterSelected,
                              width: 50,
                              selectedColor: Theme.of(context).primaryColor,
                              unselectedColor: Theme.of(context).disabledColor,
                              existingLetterColor: Colors.green, // 存在字母的颜色
                              existingLetters: _existingLetters, // 传递存在的字母集合
                            ),
                            // 播放列表
                            Expanded(
                              child: Scrollbar(
                                controller: _scrollController,
                                thumbVisibility: true,
                                interactive: true,
                                thickness: 12.0,
                                radius: const Radius.circular(4.0),
                                child: ReorderableListView.builder(
                                  scrollController: _scrollController,
                                  itemCount: audioFiles.length,
                                  onReorder: (oldIndex, newIndex) {
                                    // 当过滤模式启用时，需要将过滤后的索引转换为原始列表的索引
                                    if (_showOnlyUnplayedAudios) {
                                      // 找到过滤列表中对应项在原始列表中的索引
                                      final originalOldIndex = _currentPlaylist!
                                          .audioFiles
                                          .indexWhere((file) =>
                                              file.path ==
                                              audioFiles[oldIndex].path);

                                      // 如果新索引大于列表长度，调整为列表末尾
                                      if (newIndex > audioFiles.length) {
                                        newIndex = audioFiles.length;
                                      }

                                      // 计算新位置在原始列表中对应的位置
                                      // 如果移动到列表末尾，找到最后一个未完成项的原始索引
                                      int originalNewIndex;
                                      if (newIndex >= audioFiles.length) {
                                        // 移动到过滤列表末尾，找到最后一个未完成项
                                        originalNewIndex = _currentPlaylist!
                                            .audioFiles
                                            .lastIndexWhere(
                                                (file) => !file.isCompleted);
                                        if (originalNewIndex < 0) {
                                          originalNewIndex = _currentPlaylist!
                                                  .audioFiles.length -
                                              1;
                                        }
                                      } else if (oldIndex < newIndex) {
                                        // 修正 ReorderableListView 的行为：当 oldIndex < newIndex 时，项目会被放置在 newIndex-1 的位置
                                        final targetItemIndex = (newIndex - 1)
                                            .clamp(0, audioFiles.length - 1);
                                        originalNewIndex = _currentPlaylist!
                                            .audioFiles
                                            .indexWhere((file) =>
                                                file.path ==
                                                audioFiles[targetItemIndex]
                                                    .path);
                                      } else {
                                        originalNewIndex = _currentPlaylist!
                                            .audioFiles
                                            .indexWhere((file) =>
                                                file.path ==
                                                audioFiles[newIndex].path);
                                      }

                                      // 调用原始重排序方法，但使用转换后的索引
                                      _reorderAudioFiles(
                                          originalOldIndex, originalNewIndex);
                                    } else {
                                      // 未过滤模式下，直接使用索引
                                      _reorderAudioFiles(oldIndex, newIndex);
                                    }
                                  },
                                  itemBuilder: (context, index) {
                                    final audioFile = audioFiles[index];
                                    // 找到原始列表中的对应索引
                                    final originalIndex =
                                        _showOnlyUnplayedAudios
                                            ? _currentPlaylist!.audioFiles
                                                .indexWhere((file) =>
                                                    file.path == audioFile.path)
                                            : index;

                                    final isPlaying =
                                        originalIndex == _currentIndex &&
                                            _audioService.isPlaying;
                                    final isSelected =
                                        originalIndex == _currentIndex;

                                    // 使用一个包装器 Widget 来确保键直接附加到 ReorderableListView 的子项上
                                    return KeyedSubtree(
                                      key: ValueKey('${audioFile.path}_$index'),
                                      child: AudioTile(
                                        audioFile: audioFile,
                                        isPlaying: isPlaying,
                                        isSelected: isSelected,
                                        onTap: () {
                                          // 使用原始列表中的索引播放
                                          _playAudioAtIndex(originalIndex);
                                        },
                                        onDelete: () {}, // 播放模式下不允许删除
                                        onPlay: () {
                                          // 使用原始列表中的索引播放
                                          _playAudioAtIndex(originalIndex);
                                        },
                                        // 提供位置流而不是实际位置，让 AudioTile 内部处理 StreamBuilder
                                        positionStream: isSelected
                                            ? _audioService.positionStream
                                            : null,
                                        // 添加长按回调
                                        onLongPress: () {
                                          _toggleAudioCompleteStatus(
                                              originalIndex);
                                        },
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // 加载状态指示器
                  Visibility(
                      visible: _isLoading,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ))
                ],
              ),
        bottomNavigationBar: audioFiles.isNotEmpty
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 播放控制
                  PlayerControls(
                    audioPlayer: _audioService.audioPlayer,
                    currentAudio: currentAudio,
                    onPrevious: _playPrevious,
                    onNext: _playNext,
                    onSpeedChanged: _setPlaybackSpeed,
                    currentSpeed: _playbackSpeed,
                    onPlayPause: _togglePlayPause,
                    // 传递过滤选项和回调
                    showOnlyUnplayedAudios: _showOnlyUnplayedAudios,
                    onToggleShowOnlyUnplayedAudios:
                        _toggleShowOnlyUnplayedAudios,
                    totalAudiosCount: _currentPlaylist?.audioFiles.length ?? 0,
                    filteredAudiosCount: audioFiles.length,
                    onSeek: (position) {
                      // 使用 AudioService 的 seek 方法，而不是直接调用 audioPlayer.seek
                      // 这样会在数据库中记录拖动后的位置
                      _audioService.seek(position);
                    },
                  ),
                ],
              )
            : null,
      ),
    );
  }

  // 新增的资源清理方法，在退出页面前调用
  Future<void> _cleanupResourcesBeforeExit() async {
    _log('播放界面：退出前清理资源');

    // 取消所有定时器
    _updateTimer?.cancel();
    _updateTimer = null;

    // 保存当前播放位置
    await _saveCurrentPlayingPosition();

    // 保存播放列表状态
    await _saveFinalPlaylistState();

    // 仅暂停播放但不释放资源
    try {
      // 如果正在播放，暂停播放器
      if (_audioService.isPlaying) {
        await _audioService.pause();
        _log('播放已暂停，但保留播放器资源');
      }
    } catch (e) {
      _log('暂停播放器时出错: $e');
    }
  }

  @override
  void dispose() {
    _log('播放界面正在销毁...');

    // 停止UI更新定时器
    _updateTimer?.cancel();

    // 取消事件订阅
    _audioEventSubscription?.cancel();

    // 保存当前播放位置
    _saveCurrentPlayingPosition();

    // 移除生命周期观察者
    WidgetsBinding.instance.removeObserver(this);

    // 移除滚动控制器
    _scrollController.dispose();

    // 注意：我们不在这里完全释放AudioService，因为它可能在其他界面继续使用
    // 只需保存当前状态即可

    super.dispose();
  }

  // 确保最终状态被保存到数据库
  Future<void> _saveFinalPlaylistState() async {
    try {
      if (_currentPlaylist != null && _currentPlaylist!.id != null) {
        // 确保当前播放位置被记录
        await _audioService.saveCurrentAudioPosition();

        // 获取最新的播放列表数据
        final updatedPlaylist =
            _audioService.currentPlaylist ?? _currentPlaylist!;

        // 确保currentPlayingIndex与当前索引一致
        final finalPlaylist = updatedPlaylist.copyWith(
          currentPlayingIndex: _audioService.currentIndex,
          updatedAt: DateTime.now(),
        );

        // 将播放列表保存到数据库
        await _databaseHelper.updatePlaylist(finalPlaylist);

        _log('已保存最终播放状态到数据库: 当前播放索引=${_audioService.currentIndex}');
      }
    } catch (e) {
      _log('保存最终播放列表状态失败: $e');
    }
  }

  // 添加一个检查音频服务状态的方法
  Future<void> _checkAudioServiceState() async {
    // 如果音频服务未初始化但界面已初始化，则重新初始化
    if (!_audioService.isInitialized && _isInitialized) {
      _log('检测到音频服务未初始化，但界面已初始化，尝试重新初始化');
      _isInitialized = false;
      await _initAudio();
    }
  }

  // 新增方法，用于在回到上个页面时保存状态
  Future<bool> _onWillPop() async {
    _log('用户准备离开播放页面，正在保存状态...');

    // 保存当前位置，确保数据持久化
    await _saveCurrentPlayingPosition();

    // 允许返回
    return true;
  }
}
