import 'dart:io';

class Logger {
  // ANSI 转义码
  static const String _reset = '\x1B[0m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';

  // 检查是否支持彩色输出
  static bool get _supportsColor => stdout.supportsAnsiEscapes;

  // 彩色输出方法
  static void info(String message) {
    if (_supportsColor) {
      print('$_blue[INFO]$_reset $message');
    } else {
      print('[INFO] $message');
    }
  }

  static void success(String message) {
    if (_supportsColor) {
      print('$_green[SUCCESS]$_reset $message');
    } else {
      print('[SUCCESS] $message');
    }
  }

  static void warning(String message) {
    if (_supportsColor) {
      print('$_yellow[WARNING]$_reset $message');
    } else {
      print('[WARNING] $message');
    }
  }

  static void error(String message) {
    if (_supportsColor) {
      print('$_red[ERROR]$_reset $message');
    } else {
      print('[ERROR] $message');
    }
  }

  static void debug(String message) {
    if (_supportsColor) {
      print('$_magenta[DEBUG]$_reset $message');
    } else {
      print('[DEBUG] $message');
    }
  }

  // 自定义颜色输出
  static void custom(String message, {String color = _white}) {
    if (_supportsColor) {
      print('$color$message$_reset');
    } else {
      print(message);
    }
  }
}
