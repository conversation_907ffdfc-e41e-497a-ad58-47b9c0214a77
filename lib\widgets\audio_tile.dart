import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../models/playlist_model.dart';
import '../utils/app_theme.dart';
import '../database/database_helper.dart';
import '../services/audio_service.dart';
import 'dart:async';
import 'package:device_info_plus/device_info_plus.dart';

class AudioTile extends StatefulWidget {
  final AudioFileModel audioFile;
  final bool isPlaying;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onDelete;
  final VoidCallback? onPlay;
  final Stream<Duration>? positionStream;
  final VoidCallback? onLongPress;
  final Function(String path, int newDuration)? onDurationLoaded;
  final bool disableSystemPlayer;
  final bool hidePlayButton;

  const AudioTile({
    Key? key,
    required this.audioFile,
    this.isPlaying = false,
    this.isSelected = false,
    required this.onTap,
    required this.onDelete,
    this.onPlay,
    this.positionStream,
    this.onLongPress,
    this.onDurationLoaded,
    this.disableSystemPlayer = false,
    this.hidePlayButton = false,
  }) : super(key: key);

  @override
  State<AudioTile> createState() => _AudioTileState();
}

class _AudioTileState extends State<AudioTile> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isLoading = true;
  int _durationInSeconds = 0;
  StreamSubscription? _durationSubscription;

  @override
  void initState() {
    super.initState();
    _loadAudioDuration();
    _subscribeToDurationChanges();
  }

  void _log(String message) {
    //print(message);
  }

  void _subscribeToDurationChanges() {
    final audioService = AudioService();
    _durationSubscription =
        audioService.audioEventBus.on<AudioEvent>().listen((event) {
      if (event.eventType == AudioEventType.durationChanged &&
          event.audioFilePath == widget.audioFile.path &&
          mounted) {
        _log(
            'AudioTile接收到时长变更事件: ${widget.audioFile.fileName}, 新时长: ${event.duration?.inSeconds}秒');

        // 更新时长
        if (event.duration != null && event.duration!.inSeconds > 0) {
          final newDuration = event.duration!.inSeconds;
          if (_durationInSeconds != newDuration) {
            setState(() {
              _durationInSeconds = newDuration;
              _isLoading = false;
            });

            // 调用回调通知父组件
            if (widget.onDurationLoaded != null) {
              widget.onDurationLoaded!(widget.audioFile.path, newDuration);
            }
          }
        }
      }
    });
  }

  @override
  void didUpdateWidget(AudioTile oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.audioFile.path != widget.audioFile.path ||
        oldWidget.audioFile.durationInSeconds !=
            widget.audioFile.durationInSeconds) {
      _loadAudioDuration();
    }
  }

  Future<void> _loadAudioDuration() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // 从数据库获取初始记录的正确时长
      int duration =
          await _databaseHelper.getAudioFileDuration(widget.audioFile.path);
      // 如果数据库中没有时长记录，则使用模型中的时长
      if (duration <= 0) {
        duration = widget.audioFile.durationInSeconds;

        // 如果模型中有时长，但数据库中没有，将模型中的时长保存到数据库
        if (duration > 0) {
          final audioService = AudioService();
          await audioService.init();
          await audioService.updateAndStoreAudioDuration(
              widget.audioFile.path, Duration(seconds: duration));
          _log(
              'AudioTile保存缺失的音频时长到数据库: ${widget.audioFile.fileName}, $duration 秒');
        }
      } else if (duration != widget.audioFile.durationInSeconds) {
        // 如果数据库中的时长与模型不同，通知父组件更新
        _log(
            'AudioTile检测到时长差异: ${widget.audioFile.fileName}, 数据库: $duration 秒, 模型: ${widget.audioFile.durationInSeconds} 秒');

        // 调用回调通知父组件
        if (widget.onDurationLoaded != null) {
          widget.onDurationLoaded!(widget.audioFile.path, duration);
        }
      }

      if (mounted) {
        setState(() {
          _durationInSeconds = duration;
          _isLoading = false;
        });
      }
    } catch (e) {
      _log('AudioTile加载音频时长出错: $e');
      if (mounted) {
        setState(() {
          _durationInSeconds = widget.audioFile.durationInSeconds;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _copyFile() async {
    try {
      // 检查是否有存储权限
      if (!await _checkPermission()) {
        return;
      }

      // 让用户选择目标目录
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择保存位置',
      );

      if (selectedDirectory == null) {
        // 用户取消了选择
        return;
      }

      // 获取源文件
      final sourceFile = File(widget.audioFile.path);
      if (!await sourceFile.exists()) {
        throw Exception('源文件不存在');
      }

      // 创建目标文件路径
      final fileName = widget.audioFile.fileName;
      final targetPath = '$selectedDirectory/$fileName';

      // 检查目标文件是否已存在
      final targetFile = File(targetPath);
      if (await targetFile.exists()) {
        // 如果文件已存在，询问是否覆盖
        final bool? shouldOverwrite = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('文件已存在'),
            content: Text('$fileName 已存在于目标目录，是否覆盖？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('覆盖'),
              ),
            ],
          ),
        );

        if (shouldOverwrite != true) {
          return;
        }
      }

      // 复制文件
      await sourceFile.copy(targetPath);

      // 显示成功消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('文件已复制到: $selectedDirectory')),
        );
      }
    } catch (e) {
      String errorMessage = '复制失败: $e';

      // 检查是否是华为设备缺少权限的特定错误
      if (e.toString().contains('Permission') ||
          e.toString().contains('permission') ||
          e.toString().contains('权限')) {
        errorMessage = '复制失败: 可能需要在设置中授予"管理外部存储权限"，特别是华为设备需要此权限';
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: '设置',
              onPressed: () => openAppSettings(),
            ),
          ),
        );
      }
    }
  }

  Future<bool> _checkPermission() async {
    try {
      // 检查设备类型
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        final manufacturer = androidInfo.manufacturer.toLowerCase();
        final isHuawei =
            manufacturer.contains('huawei') || manufacturer.contains('honor');

        if (isHuawei) {
          // 华为设备需要检查MANAGE_EXTERNAL_STORAGE权限
          final manageExternalStatus =
              await Permission.manageExternalStorage.status;
          if (!manageExternalStatus.isGranted) {
            // 请求华为特殊权限
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('华为设备需要授予"管理外部存储权限"才能复制文件'),
                  duration: const Duration(seconds: 5),
                  action: SnackBarAction(
                    label: '设置',
                    onPressed: () => openAppSettings(),
                  ),
                ),
              );
            }

            final result = await Permission.manageExternalStorage.request();
            return result.isGranted;
          }
          return true;
        }
      }

      // 其他设备使用标准存储权限
      final status = await Permission.storage.status;
      if (!status.isGranted) {
        final result = await Permission.storage.request();
        if (!result.isGranted && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('需要存储权限才能复制文件'),
              action: SnackBarAction(
                label: '设置',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
        return result.isGranted;
      }
      return true;
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('检查权限失败: $e')),
        );
      }
      return false;
    }
  }

  Future<void> _deleteFile() async {
    try {
      // 显示确认对话框
      final bool? confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要从列表中删除 ${widget.audioFile.fileName} 吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('删除'),
            ),
          ],
        ),
      );

      if (confirm == true) {
        // 如果当前正在播放这个文件，先停止播放
        if (widget.isPlaying) {
          final audioService = AudioService();
          await audioService.stop();
        }

        // 先通知父组件文件已删除，让UI立即更新
        widget.onDelete();

        // 从数据库中删除文件
        final deletedCount =
            await _databaseHelper.deleteAudioFile(widget.audioFile.path);

        // 显示成功或失败的消息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(deletedCount > 0 ? '已从列表中删除' : '删除失败：文件不存在')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败：$e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Duration>(
      stream: widget.positionStream,
      builder: (context, snapshot) {
        int positionInSeconds;
        double progressPercent;

        final actualDurationInSeconds = _isLoading
            ? widget.audioFile.durationInSeconds
            : _durationInSeconds;

        if (widget.isPlaying && widget.isSelected && snapshot.hasData) {
          positionInSeconds = snapshot.data!.inSeconds;
          progressPercent = actualDurationInSeconds > 0
              ? positionInSeconds / actualDurationInSeconds
              : 0.0;
        } else {
          positionInSeconds = widget.audioFile.lastPositionInSeconds;
          progressPercent = actualDurationInSeconds > 0
              ? widget.audioFile.lastPositionInSeconds / actualDurationInSeconds
              : 0.0;
        }

        final formattedDuration = _formatDuration(actualDurationInSeconds);
        final formattedPosition = _formatDuration(positionInSeconds);

        return Slidable(
          endActionPane: ActionPane(
            motion: const ScrollMotion(),
            children: [
              SlidableAction(
                onPressed: (_) => _deleteFile(),
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                icon: Icons.delete,
                label: '删除',
              ),
              SlidableAction(
                onPressed: (_) => _copyFile(),
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                icon: Icons.copy,
                label: '复制',
              ),
            ],
          ),
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            height: 120, // 固定高度，确保所有项目高度一致
            decoration: BoxDecoration(
              color: widget.isSelected
                  ? AppTheme.primaryColor.withOpacity(0.1)
                  : Colors.white,
              borderRadius: AppTheme.defaultRadius,
              boxShadow: widget.isSelected ? AppTheme.defaultShadow : null,
              border: widget.audioFile.isCompleted
                  ? Border.all(color: AppTheme.completedColor, width: 1)
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.disableSystemPlayer ? null : widget.onTap,
                onLongPress: widget.onLongPress,
                borderRadius: AppTheme.defaultRadius,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment:
                        MainAxisAlignment.spaceBetween, // 确保元素均匀分布
                    children: [
                      // 上半部分 - 文件名和进度
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐
                        children: [
                          if (widget.isPlaying)
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.music_note,
                                color: Colors.white,
                                size: 14,
                              ),
                            )
                          else
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: widget.audioFile.isCompleted
                                    ? AppTheme.completedColor
                                    : AppTheme.textTertiaryColor,
                                shape: BoxShape.circle,
                              ),
                              child: Text(
                                '${widget.audioFile.orderIndex + 1}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.audioFile.fileName,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: widget.audioFile.isCompleted
                                        ? AppTheme.completedColor
                                        : AppTheme.textPrimaryColor,
                                    decoration: widget.audioFile.isCompleted
                                        ? TextDecoration.lineThrough
                                        : null,
                                    fontSize: 13, // 稍微减小字体大小
                                  ),
                                  maxLines: 2, // 改为两行显示
                                  overflow: TextOverflow.ellipsis,
                                  // 设置固定高度，确保即使是单行文本也占用两行高度
                                  strutStyle: const StrutStyle(
                                    forceStrutHeight: true,
                                    height: 1.2,
                                    leading: 0.5,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      formattedPosition,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                    Text(
                                      ' / ',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                    Text(
                                      formattedDuration,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.textSecondaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          if (widget.audioFile.isCompleted)
                            const Icon(
                              Icons.check_circle,
                              color: AppTheme.completedColor,
                            ),
                        ],
                      ),

                      // 中间部分 - 长按提示
                      if (widget.onLongPress != null)
                        Text(
                          '长按可${widget.audioFile.isCompleted ? '标记为未听完' : '标记为已听完'}',
                          style: TextStyle(
                            fontSize: 10,
                            color: AppTheme.textTertiaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),

                      // 底部部分 - 进度条
                      LinearPercentIndicator(
                        lineHeight: 4,
                        percent: progressPercent.clamp(0.0, 1.0),
                        backgroundColor:
                            AppTheme.incompleteColor.withOpacity(0.2),
                        progressColor: widget.isPlaying
                            ? AppTheme.primaryColor
                            : (widget.audioFile.isCompleted
                                ? AppTheme.completedColor
                                : AppTheme.secondaryColor),
                        padding: EdgeInsets.zero,
                        barRadius: const Radius.circular(2),
                        animation: widget.isPlaying,
                        animationDuration: 1000,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    // 取消事件订阅
    _durationSubscription?.cancel();
    super.dispose();
  }
}
