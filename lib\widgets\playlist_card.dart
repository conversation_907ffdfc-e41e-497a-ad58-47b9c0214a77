import 'package:flutter/material.dart';
import 'dart:async'; // 添加Timer支持
import '../models/playlist_model.dart';
import '../utils/app_theme.dart';
import '../database/database_helper.dart';

// 定义音频完成状态变化的回调类型
typedef AudioCompletedCallback = void Function(AudioFileModel audioFile);

class PlaylistCard extends StatefulWidget {
  final PlaylistModel playlist;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const PlaylistCard({
    Key? key,
    required this.playlist,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  }) : super(key: key);

  @override
  State<PlaylistCard> createState() => _PlaylistCardState();
}

class _PlaylistCardState extends State<PlaylistCard> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  int _totalDurationInSeconds = 0;
  bool _isLoading = true;
  Timer? _refreshTimer;

  // 添加日志函数
  void _log(String message) {
    // 暂时禁用输出，仅在database_helper中保留
    // 根据需要可以添加额外的日志处理逻辑
    // print(message);
  }

  @override
  void initState() {
    super.initState();
    _loadAudioDurations();

    // 每60秒刷新一次时长显示，确保在播放期间更新的时长能够反映到列表中
    _refreshTimer = Timer.periodic(const Duration(seconds: 60), (timer) {
      if (mounted) {
        _loadAudioDurations();
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(PlaylistCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 检查是否需要重新加载音频时长：
    // 1. 播放列表更新时间变化
    // 2. 播放列表ID变化
    // 3. 音频文件数量变化
    if (oldWidget.playlist.updatedAt != widget.playlist.updatedAt ||
        oldWidget.playlist.id != widget.playlist.id ||
        oldWidget.playlist.audioFiles.length !=
            widget.playlist.audioFiles.length) {
      _loadAudioDurations();
    }
  }

  Future<void> _loadAudioDurations() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    int totalDuration = 0;
    for (var audioFile in widget.playlist.audioFiles) {
      try {
        // 从数据库获取初始记录的正确时长
        int duration =
            await _databaseHelper.getAudioFileDuration(audioFile.path);

        // 如果数据库中没有时长记录，则使用模型中的时长
        if (duration <= 0) {
          duration = audioFile.durationInSeconds;

          // 如果模型中有时长，但数据库中没有，将模型中的时长保存到数据库
          if (duration > 0) {
            await _databaseHelper.updateAudioFileDuration(
                audioFile.path, duration);
            _log(
                'PlaylistCard保存缺失的音频时长到数据库: ${audioFile.fileName}, $duration 秒');
          }
        } else if (duration != audioFile.durationInSeconds) {
          // 如果数据库中的时长与模型不同，记录差异日志
          _log(
              'PlaylistCard检测到时长差异: ${audioFile.fileName}, 数据库: $duration 秒, 模型: ${audioFile.durationInSeconds} 秒');

          // 对于时长差异较大的情况，考虑通知用户或管理员
          if (duration > 0 &&
              audioFile.durationInSeconds > 0 &&
              ((duration - audioFile.durationInSeconds).abs() / duration >
                  0.2)) {
            _log('时长差异超过20%，可能需要人工检查文件: ${audioFile.fileName}');
          }
        }

        totalDuration += duration;
      } catch (e) {
        _log('PlaylistCard加载音频时长出错: $e');
        // 出错时使用模型中的时长
        totalDuration += audioFile.durationInSeconds;
      }
    }

    if (mounted) {
      setState(() {
        _totalDurationInSeconds = totalDuration;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 计算播放进度
    int totalFiles = widget.playlist.audioFiles.length;
    int completedFiles =
        widget.playlist.audioFiles.where((file) => file.isCompleted).length;
    double completionProgress =
        totalFiles > 0 ? completedFiles / totalFiles : 0.0;

    // 计算已播放的时长
    int playedDurationInSeconds = widget.playlist.audioFiles
        .fold(0, (sum, file) => sum + file.lastPositionInSeconds);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: AppTheme.defaultRadius,
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: AppTheme.defaultRadius,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.queue_music,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.playlist.name,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$totalFiles 个文件 · ${_isLoading ? "计算中..." : _formatDuration(_totalDurationInSeconds)}',
                          style: TextStyle(
                            color: AppTheme.textSecondaryColor,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        widget.onEdit();
                      } else if (value == 'delete') {
                        widget.onDelete();
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('编辑'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('删除', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: completionProgress,
                  backgroundColor: AppTheme.incompleteColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(
                    completionProgress == 1.0
                        ? AppTheme.completedColor
                        : AppTheme.primaryColor,
                  ),
                  minHeight: 8,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '已完成: $completedFiles/$totalFiles',
                    style: TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '已播放: ${_formatDuration(playedDurationInSeconds)}/${_isLoading ? "计算中..." : _formatDuration(_totalDurationInSeconds)}',
                    style: TextStyle(
                      color: AppTheme.textSecondaryColor,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              if (widget.playlist.audioFiles.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  '最近文件:',
                  style: TextStyle(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                ...widget.playlist.audioFiles.take(2).map((file) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Icon(
                            file.isCompleted
                                ? Icons.check_circle
                                : Icons.music_note,
                            size: 16,
                            color: file.isCompleted
                                ? AppTheme.completedColor
                                : AppTheme.primaryColor,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              file.fileName,
                              style: TextStyle(
                                fontSize: 12,
                                color: file.isCompleted
                                    ? AppTheme.completedColor
                                    : AppTheme.textPrimaryColor,
                                decoration: file.isCompleted
                                    ? TextDecoration.lineThrough
                                    : null,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    )),
                if (widget.playlist.audioFiles.length > 2)
                  Text(
                    '... 还有 ${widget.playlist.audioFiles.length - 2} 个文件',
                    style: TextStyle(
                      color: AppTheme.textTertiaryColor,
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final remainingSeconds = seconds % 60;

    if (hours > 0) {
      return '$hours:${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
    } else {
      return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
    }
  }
}
