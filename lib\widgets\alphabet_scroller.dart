import 'package:flutter/material.dart';

class AlphabetScroller extends StatelessWidget {
  final Function(String) onLetterSelected;
  final List<String> letters;
  final double width;
  final Color selectedColor;
  final Color unselectedColor;
  final Color existingLetterColor;
  final TextStyle textStyle;
  final Set<String> existingLetters; // 存在于列表中的字母

  const AlphabetScroller({
    Key? key,
    required this.onLetterSelected,
    required this.letters,
    this.width = 50,
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.existingLetterColor = Colors.green,
    this.existingLetters = const {},
    this.textStyle = const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取可用高度
        final availableHeight = constraints.maxHeight;

        // 计算每个字母项的高度
        final itemHeight = availableHeight / letters.length;

        // 根据项目高度动态调整字体大小，但设置最小和最大值
        final fontSize = (itemHeight * 0.6).clamp(10.0, 18.0);

        return Container(
          width: width,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: ListView.builder(
            physics:
                const NeverScrollableScrollPhysics(), // 禁用滚动，因为我们已经计算了精确的高度
            itemCount: letters.length,
            itemBuilder: (context, index) {
              final letter = letters[index];
              // 判断字母是否存在于列表中
              final bool isExisting = existingLetters.contains(letter);

              return GestureDetector(
                onTap: () => onLetterSelected(letter),
                child: Container(
                  height: itemHeight,
                  alignment: Alignment.center,
                  child: Text(
                    letter,
                    style: textStyle.copyWith(
                      fontSize: fontSize,
                      // 如果字母存在于列表中，使用绿色，否则使用默认颜色
                      color: isExisting ? existingLetterColor : unselectedColor,
                      // 如果字母存在于列表中，使用粗体
                      fontWeight:
                          isExisting ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

// 辅助类，用于生成字母列表和查找文件的首字母
class AlphabetHelper {
  // 生成字母列表，只包括英文字母
  static List<String> generateLetters() {
    List<String> letters = [];

    // 添加向上箭头，用于滚动到顶部
    letters.add('↑'); // 向上箭头符号

    // 添加英文字母 A-Z
    for (int i = 65; i <= 90; i++) {
      letters.add(String.fromCharCode(i));
    }

    // 添加特殊标记，用于其他字符
    letters.add('#');

    return letters;
  }

  // 获取文件名的首字母（大写）
  static String getFirstLetter(String fileName) {
    if (fileName.isEmpty) return '#';

    // 移除路径，只保留文件名
    String name = fileName.split('/').last;

    // 移除文件扩展名
    if (name.contains('.')) {
      name = name.substring(0, name.lastIndexOf('.'));
    }

    // 获取第一个字符
    String firstChar = name.isNotEmpty ? name[0].toUpperCase() : '#';

    // 只检查是否是字母，数字和其他字符都返回 #
    if (RegExp(r'[A-Z]').hasMatch(firstChar)) {
      return firstChar;
    }

    // 对于数字、中文和其他字符，返回 #
    return '#';
  }
}
