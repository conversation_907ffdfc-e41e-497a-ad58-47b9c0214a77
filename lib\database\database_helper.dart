import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart'; // 导入foundation.dart获取kDebugMode
import '../models/playlist_model.dart';

// 添加日志函数，在非调试模式下不输出
void _log(String message) {
  //if (kDebugMode) {
  //  print(message);
  //}
}

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;

  static Database? _database;

  // 添加调试标志
  bool _enableSqlLogging = true; // 设置为true启用SQL日志

  DatabaseHelper._internal();

  // 添加方法，用于控制SQL日志输出
  void setSqlLogging(bool enable) {
    _enableSqlLogging = enable;
  }

  // 日志输出辅助方法
  void _logSql(String operation, String sql, [List<dynamic>? args]) {
    if (_enableSqlLogging && kDebugMode) {
      String logMessage = '=== SQL 执行 [$operation] ===\n$sql';
      if (args != null && args.isNotEmpty) {
        logMessage += '\n参数: $args';
      }
      _log(logMessage);
    }
  }

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // 添加同步获取数据库的方法
  Database? getDatabaseSync() {
    return _database;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'audio_player.db');
    return await openDatabase(
      path,
      version: 4,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // 播放列表表
    final createPlaylistsTable = '''
      CREATE TABLE playlists(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        audioFilesJson TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        currentPlayingIndex INTEGER NOT NULL DEFAULT 0,
        playback_speed REAL NOT NULL DEFAULT 1.0
      )
    ''';
    _logSql('CREATE TABLE', createPlaylistsTable);
    await db.execute(createPlaylistsTable);

    // 播放历史表
    final createHistoryTable = '''
      CREATE TABLE play_history(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        audioFilePath TEXT NOT NULL,
        playlistId INTEGER,
        position INTEGER NOT NULL,
        playedAt INTEGER NOT NULL,
        FOREIGN KEY(playlistId) REFERENCES playlists(id)
      )
    ''';
    _logSql('CREATE TABLE', createHistoryTable);
    await db.execute(createHistoryTable);

    // 添加音频文件表，专门存储音频时长信息
    final createAudioFilesTable = '''
      CREATE TABLE audio_files(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        path TEXT UNIQUE NOT NULL,
        duration_in_seconds INTEGER NOT NULL DEFAULT 0,
        initial_duration_in_seconds INTEGER NOT NULL DEFAULT 0,
        last_updated INTEGER NOT NULL
      )
    ''';
    _logSql('CREATE TABLE', createAudioFilesTable);
    await db.execute(createAudioFilesTable);
  }

  // 添加数据库升级处理
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // 版本1升级到版本2：添加音频文件表
      try {
        _log('数据库升级: 从版本 $oldVersion 升级到 $newVersion, 添加audio_files表');

        // 检查表是否已存在
        final tables = await db.rawQuery(
            "SELECT name FROM sqlite_master WHERE type='table' AND name='audio_files'");
        if (tables.isEmpty) {
          await db.execute('''
            CREATE TABLE audio_files(
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              path TEXT UNIQUE NOT NULL,
              duration_in_seconds INTEGER NOT NULL DEFAULT 0,
              initial_duration_in_seconds INTEGER NOT NULL DEFAULT 0,
              last_updated INTEGER NOT NULL
            )
          ''');
          _log('成功创建audio_files表');
        } else {
          _log('audio_files表已存在，跳过创建');
        }
      } catch (e) {
        _log('创建audio_files表失败: $e');
      }
    }

    // 版本2升级到版本3：为playlists表添加currentPlayingIndex字段
    if (oldVersion < 3) {
      try {
        _log('数据库升级: 从版本 $oldVersion 升级到 $newVersion, 添加currentPlayingIndex字段');

        // 检查playlists表中是否已有currentPlayingIndex字段
        final columns = await db.rawQuery("PRAGMA table_info(playlists)");
        final hasColumn =
            columns.any((col) => col['name'] == 'currentPlayingIndex');

        if (!hasColumn) {
          await db.execute('''
            ALTER TABLE playlists ADD COLUMN currentPlayingIndex INTEGER NOT NULL DEFAULT 0
          ''');
          _log('成功添加currentPlayingIndex字段到playlists表');
        } else {
          _log('currentPlayingIndex字段已存在，跳过添加');
        }
      } catch (e) {
        _log('添加currentPlayingIndex字段失败: $e');
      }
    }

    // 版本3升级到版本4：为playlists表添加playback_speed字段
    if (oldVersion < 4) {
      try {
        _log('数据库升级: 从版本 $oldVersion 升级到 $newVersion, 添加playback_speed字段');

        // 检查playlists表中是否已有playback_speed字段
        final columns = await db.rawQuery("PRAGMA table_info(playlists)");
        final hasColumn = columns.any((col) => col['name'] == 'playback_speed');

        if (!hasColumn) {
          await db.execute('''
            ALTER TABLE playlists ADD COLUMN playback_speed REAL NOT NULL DEFAULT 1.0
          ''');
          _log('成功添加playback_speed字段到playlists表');
        } else {
          _log('playback_speed字段已存在，跳过添加');
        }
      } catch (e) {
        _log('添加playback_speed字段失败: $e');
      }
    }
  }

  // 播放列表相关操作

  Future<int> insertPlaylist(PlaylistModel playlist) async {
    final db = await database;
    final map = playlist.toMap();
    _logSql('INSERT', 'INSERT INTO playlists VALUES(?)', [map]);
    return await db.insert('playlists', map);
  }

  Future<int> updatePlaylist(PlaylistModel playlist) async {
    final db = await database;
    final map = playlist.toMap();
    _logSql(
        'UPDATE', 'UPDATE playlists SET ? WHERE id = ${playlist.id}', [map]);
    return await db.update(
      'playlists',
      map,
      where: 'id = ?',
      whereArgs: [playlist.id],
    );
  }

  Future<int> deletePlaylist(int id) async {
    final db = await database;

    // 首先删除play_history表中与该播放列表相关的记录
    _logSql('DELETE', 'DELETE FROM play_history WHERE playlistId = ?', [id]);
    await db.delete(
      'play_history',
      where: 'playlistId = ?',
      whereArgs: [id],
    );

    // 然后删除播放列表本身
    _logSql('DELETE', 'DELETE FROM playlists WHERE id = ?', [id]);
    return await db.delete(
      'playlists',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<PlaylistModel?> getPlaylist(int id) async {
    final db = await database;
    _logSql('QUERY', 'SELECT * FROM playlists WHERE id = ?', [id]);
    final List<Map<String, dynamic>> maps = await db.query(
      'playlists',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      // 获取基础播放列表数据
      PlaylistModel playlist = PlaylistModel.fromMap(maps.first);

      // 如果有音频文件，从play_history表获取最新的播放位置
      if (playlist.audioFiles.isNotEmpty) {
        List<AudioFileModel> updatedAudioFiles = [];

        // 处理每个音频文件
        for (var audioFile in playlist.audioFiles) {
          // 从play_history表获取该文件在该播放列表中的最新播放位置
          int lastPosition =
              await getLastPositionForAudio(audioFile.path, playlistId: id);

          // 更新音频文件的最后播放位置
          updatedAudioFiles.add(audioFile.copyWith(
            lastPositionInSeconds: lastPosition,
          ));
        }

        // 使用更新后的音频文件创建新的播放列表对象
        playlist = playlist.copyWith(audioFiles: updatedAudioFiles);
      }

      return playlist;
    }
    return null;
  }

  Future<PlaylistModel?> getPlaylistById(int id) async {
    return getPlaylist(id);
  }

  Future<List<PlaylistModel>> getAllPlaylists() async {
    final db = await database;

    _logSql('QUERY', 'SELECT * FROM playlists');

    final List<Map<String, dynamic>> maps = await db.query('playlists');

    return List.generate(maps.length, (i) {
      return PlaylistModel.fromMap(maps[i]);
    });
  }

  // 播放历史相关操作

  Future<int> insertPlayHistory(
      String audioFilePath, int? playlistId, int position) async {
    final db = await database;
    return await db.insert('play_history', {
      'audioFilePath': audioFilePath,
      'playlistId': playlistId,
      'position': position,
      'playedAt': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<List<Map<String, dynamic>>> getRecentPlayHistory(int limit) async {
    final db = await database;
    return await db.query(
      'play_history',
      orderBy: 'playedAt DESC',
      limit: limit,
    );
  }

  Future<int> updateAudioFilePosition(String audioFilePath, int position,
      {int? playlistId}) async {
    final db = await database;

    // 如果提供了播放列表ID，则同时使用文件路径和播放列表ID作为条件
    if (playlistId != null) {
      // 构建SQL日志
      final updateSql = '''
        UPDATE play_history 
        SET position = $position, playedAt = ${DateTime.now().millisecondsSinceEpoch}
        WHERE audioFilePath = '$audioFilePath' AND playlistId = $playlistId
      ''';
      _logSql('UPDATE', updateSql);

      // 先尝试更新，如果没有对应记录会返回0
      final updateCount = await db.update(
        'play_history',
        {
          'position': position,
          'playedAt': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'audioFilePath = ? AND playlistId = ?',
        whereArgs: [audioFilePath, playlistId],
      );

      // 如果没有更新任何记录，尝试插入新记录
      if (updateCount == 0) {
        final insertSql = '''
          INSERT INTO play_history(audioFilePath, playlistId, position, playedAt)
          VALUES('$audioFilePath', $playlistId, $position, ${DateTime.now().millisecondsSinceEpoch})
        ''';
        _logSql('INSERT', insertSql);

        await db.insert('play_history', {
          'audioFilePath': audioFilePath,
          'playlistId': playlistId,
          'position': position,
          'playedAt': DateTime.now().millisecondsSinceEpoch,
        });
        return 1; // 返回1表示成功插入一条记录
      }

      return updateCount;
    } else {
      // 向后兼容：如果没有提供播放列表ID，则只使用文件路径作为条件
      final updateSql = '''
        UPDATE play_history 
        SET position = $position, playedAt = ${DateTime.now().millisecondsSinceEpoch}
        WHERE audioFilePath = '$audioFilePath'
      ''';
      _logSql('UPDATE', updateSql);

      return await db.update(
        'play_history',
        {
          'position': position,
          'playedAt': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'audioFilePath = ?',
        whereArgs: [audioFilePath],
      );
    }
  }

  Future<int> getLastPositionForAudio(String audioFilePath,
      {int? playlistId}) async {
    final db = await database;
    List<Map<String, dynamic>> result;

    // 如果提供了播放列表ID，则同时使用文件路径和播放列表ID作为条件
    if (playlistId != null) {
      final querySql = '''
        SELECT position FROM play_history 
        WHERE audioFilePath = '$audioFilePath' AND playlistId = $playlistId
        ORDER BY playedAt DESC LIMIT 1
      ''';
      _logSql('QUERY', querySql);

      result = await db.query(
        'play_history',
        columns: ['position'],
        where: 'audioFilePath = ? AND playlistId = ?',
        whereArgs: [audioFilePath, playlistId],
        orderBy: 'playedAt DESC',
        limit: 1,
      );

      // 如果找不到特定播放列表ID的记录，尝试查找没有播放列表ID的记录（向后兼容）
      if (result.isEmpty) {
        final fallbackSql = '''
          SELECT position FROM play_history 
          WHERE audioFilePath = '$audioFilePath' AND playlistId IS NULL
          ORDER BY playedAt DESC LIMIT 1
        ''';
        _logSql('QUERY', fallbackSql);

        result = await db.query(
          'play_history',
          columns: ['position'],
          where: 'audioFilePath = ? AND playlistId IS NULL',
          whereArgs: [audioFilePath],
          orderBy: 'playedAt DESC',
          limit: 1,
        );
      }
    } else {
      // 向后兼容：如果没有提供播放列表ID，只查找文件路径对应的记录
      final querySql = '''
        SELECT position FROM play_history 
        WHERE audioFilePath = '$audioFilePath'
        ORDER BY playedAt DESC LIMIT 1
      ''';
      _logSql('QUERY', querySql);

      result = await db.query(
        'play_history',
        columns: ['position'],
        where: 'audioFilePath = ?',
        whereArgs: [audioFilePath],
        orderBy: 'playedAt DESC',
        limit: 1,
      );
    }

    if (result.isNotEmpty) {
      return result.first['position'] as int;
    }
    return 0;
  }

  // 新增方法：获取指定音频文件在所有播放列表中的播放位置
  Future<Map<int, int>> getAllPlaylistPositionsForAudio(
      String audioFilePath) async {
    final db = await database;

    final querySql = '''
      SELECT playlistId, position FROM play_history
      WHERE audioFilePath = '$audioFilePath' AND playlistId IS NOT NULL
      ORDER BY playedAt DESC
    ''';
    _logSql('QUERY', querySql);

    final result = await db.query(
      'play_history',
      columns: ['playlistId', 'position'],
      where: 'audioFilePath = ? AND playlistId IS NOT NULL',
      whereArgs: [audioFilePath],
      orderBy: 'playedAt DESC',
    );

    // 创建播放列表ID到播放位置的映射
    final Map<int, int> playlistPositions = {};

    // 对结果进行分组处理，每个播放列表只保留最新的播放位置
    for (var record in result) {
      final playlistId = record['playlistId'] as int;
      if (!playlistPositions.containsKey(playlistId)) {
        playlistPositions[playlistId] = record['position'] as int;
      }
    }

    return playlistPositions;
  }

  // 新增方法：获取播放列表名称
  Future<String?> getPlaylistName(int playlistId) async {
    final db = await database;

    final querySql = "SELECT name FROM playlists WHERE id = $playlistId";
    _logSql('QUERY', querySql);

    final result = await db.query(
      'playlists',
      columns: ['name'],
      where: 'id = ?',
      whereArgs: [playlistId],
    );

    if (result.isNotEmpty) {
      return result.first['name'] as String;
    }
    return null;
  }

  // 更新音频文件的时长
  Future<int> updateAudioFileDuration(
      String audioFilePath, int durationInSeconds) async {
    final db = await database;

    // 查询SQL日志
    final checkSql = "SELECT * FROM audio_files WHERE path = '$audioFilePath'";
    _logSql('QUERY', checkSql);

    // 先检查音频文件记录是否存在
    final List<Map<String, dynamic>> existing = await db.query(
      'audio_files',
      where: 'path = ?',
      whereArgs: [audioFilePath],
    );

    int updateResult = 0;

    if (existing.isEmpty) {
      // 如果记录不存在，插入新记录
      try {
        final insertSql = '''
          INSERT INTO audio_files(path, duration_in_seconds, initial_duration_in_seconds, last_updated)
          VALUES('$audioFilePath', $durationInSeconds, $durationInSeconds, ${DateTime.now().millisecondsSinceEpoch})
        ''';
        _logSql('INSERT', insertSql);

        updateResult = await db.insert(
          'audio_files',
          {
            'path': audioFilePath,
            'duration_in_seconds': durationInSeconds,
            'initial_duration_in_seconds': durationInSeconds,
            'last_updated': DateTime.now().millisecondsSinceEpoch
          },
        );
        _log('插入新的音频文件时长记录: $audioFilePath, $durationInSeconds 秒');
      } catch (e) {
        _log('插入音频文件时长记录失败: $e');
      }
    } else {
      // 如果记录存在，获取当前记录中的初始时长
      final initialDuration =
          existing.first['initial_duration_in_seconds'] as int;

      // 如果当前没有初始时长，或者新时长与当前时长差异很大，更新初始时长
      if (initialDuration <= 0 ||
          (initialDuration - durationInSeconds).abs() > initialDuration * 0.2) {
        final updateSql = '''
          UPDATE audio_files 
          SET duration_in_seconds = $durationInSeconds, 
              initial_duration_in_seconds = $durationInSeconds,
              last_updated = ${DateTime.now().millisecondsSinceEpoch}
          WHERE path = '$audioFilePath'
        ''';
        _logSql('UPDATE', updateSql);

        updateResult = await db.update(
          'audio_files',
          {
            'duration_in_seconds': durationInSeconds,
            'initial_duration_in_seconds': durationInSeconds,
            'last_updated': DateTime.now().millisecondsSinceEpoch
          },
          where: 'path = ?',
          whereArgs: [audioFilePath],
        );
      } else {
        // 否则只更新当前时长，保留初始时长
        final updateSql = '''
          UPDATE audio_files 
          SET duration_in_seconds = $durationInSeconds,
              last_updated = ${DateTime.now().millisecondsSinceEpoch}
          WHERE path = '$audioFilePath'
        ''';
        _logSql('UPDATE', updateSql);

        updateResult = await db.update(
          'audio_files',
          {
            'duration_in_seconds': durationInSeconds,
            'last_updated': DateTime.now().millisecondsSinceEpoch
          },
          where: 'path = ?',
          whereArgs: [audioFilePath],
        );
      }
    }

    // 查找所有包含该音频文件的播放列表
    final List<Map<String, dynamic>> playlists = await db.query('playlists');

    for (var playlistMap in playlists) {
      try {
        // 将播放列表转换为对象
        final playlist = PlaylistModel.fromMap(playlistMap);

        // 查找该音频文件在播放列表中的索引
        final index = playlist.audioFiles
            .indexWhere((file) => file.path == audioFilePath);

        // 如果找到了该音频文件
        if (index >= 0) {
          // 获取当前文件
          final audioFile = playlist.audioFiles[index];

          // 如果持续时间不同，则更新
          if (audioFile.durationInSeconds != durationInSeconds) {
            // 创建新的音频文件列表
            final updatedAudioFiles =
                List<AudioFileModel>.from(playlist.audioFiles);

            // 更新持续时间
            updatedAudioFiles[index] = audioFile.copyWith(
              durationInSeconds: durationInSeconds,
            );

            // 创建更新后的播放列表
            final updatedPlaylist = playlist.copyWith(
              audioFiles: updatedAudioFiles,
              updatedAt: DateTime.now(),
            );

            // 更新数据库
            await updatePlaylist(updatedPlaylist);
          }
        }
      } catch (e) {
        _log('更新音频文件持续时间时出错: $e');
      }
    }

    return updateResult;
  }

  // 获取音频文件的时长
  Future<int> getAudioFileDuration(String path) async {
    final db = await database;

    try {
      // 查询audio_files表
      final querySql =
          "SELECT initial_duration_in_seconds, duration_in_seconds FROM audio_files WHERE path = '$path'";
      _logSql('QUERY', querySql);

      final resultFromAudioFiles = await db.query(
        'audio_files',
        columns: ['initial_duration_in_seconds', 'duration_in_seconds'],
        where: 'path = ?',
        whereArgs: [path],
      );

      if (resultFromAudioFiles.isNotEmpty) {
        // 优先使用当前时长(duration_in_seconds)，这是最准确的时长
        final currentDuration =
            resultFromAudioFiles.first['duration_in_seconds'] as int? ?? 0;

        // 如果当前时长有效，直接返回
        if (currentDuration > 0) {
          return currentDuration;
        }

        // 否则尝试使用初始时长
        final initialDuration =
            resultFromAudioFiles.first['initial_duration_in_seconds'] as int? ??
                0;

        if (initialDuration > 0) {
          return initialDuration;
        }
      }

      // 如果在audio_files表中没有找到，尝试从播放列表中获取
      _logSql('QUERY', 'SELECT * FROM playlists');
      final List<Map<String, dynamic>> playlists = await db.query('playlists');
      for (var playlistMap in playlists) {
        try {
          final playlist = PlaylistModel.fromMap(playlistMap);
          final audioFile = playlist.audioFiles.firstWhere(
            (file) => file.path == path,
            orElse: () => AudioFileModel(
              path: '',
              fileName: '',
              orderIndex: 0,
              durationInSeconds: 0,
              lastPositionInSeconds: 0,
              isCompleted: false,
            ),
          );

          if (audioFile.path.isNotEmpty && audioFile.durationInSeconds > 0) {
            // 找到时长值后，立即存入audio_files表
            await updateAudioFileDuration(path, audioFile.durationInSeconds);
            return audioFile.durationInSeconds;
          }
        } catch (e) {
          _log('从播放列表获取音频时长失败: $e');
        }
      }
    } catch (e) {
      _log('获取音频文件时长失败: $e');
    }

    return 0;
  }

  // 更新所有包含指定音频文件的播放列表
  Future<void> updateAllPlaylistsWithAudioFile(
      String filePath, int durationInSeconds) async {
    final db = await database;

    try {
      // 查找所有包含该音频文件的播放列表
      final List<Map<String, dynamic>> playlists = await db.query('playlists');
      int updatedCount = 0;

      for (var playlistMap in playlists) {
        try {
          // 将播放列表转换为对象
          final playlist = PlaylistModel.fromMap(playlistMap);

          // 查找该音频文件在播放列表中的索引
          final index =
              playlist.audioFiles.indexWhere((file) => file.path == filePath);

          // 如果找到了该音频文件
          if (index >= 0) {
            // 获取当前文件
            final audioFile = playlist.audioFiles[index];

            // 如果持续时间不同，则更新
            if (audioFile.durationInSeconds != durationInSeconds) {
              _log(
                  '更新播放列表 ${playlist.name} 中的音频文件时长: ${audioFile.fileName}, 从 ${audioFile.durationInSeconds}秒 到 ${durationInSeconds}秒');

              // 创建新的音频文件列表
              final updatedAudioFiles =
                  List<AudioFileModel>.from(playlist.audioFiles);

              // 更新持续时间
              updatedAudioFiles[index] = audioFile.copyWith(
                durationInSeconds: durationInSeconds,
              );

              // 创建更新后的播放列表
              final updatedPlaylist = playlist.copyWith(
                audioFiles: updatedAudioFiles,
                updatedAt: DateTime.now(),
              );

              // 更新数据库
              await updatePlaylist(updatedPlaylist);
              updatedCount++;
            }
          }
        } catch (e) {
          _log('更新播放列表中的音频文件时长出错: $e');
        }
      }

      _log('共更新了 $updatedCount 个播放列表中的音频文件时长');
    } catch (e) {
      _log('更新播放列表音频时长出错: $e');
    }
  }

  // 删除音频文件
  Future<int> deleteAudioFile(String filePath) async {
    final db = await database;

    try {
      // 1. 从 audio_files 表中删除记录
      final deleteFromAudioFiles = await db.delete(
        'audio_files',
        where: 'path = ?',
        whereArgs: [filePath],
      );
      _log('从 audio_files 表删除记录: $deleteFromAudioFiles 行');

      // 2. 从 play_history 表中删除相关记录
      final deleteFromHistory = await db.delete(
        'play_history',
        where: 'audioFilePath = ?',
        whereArgs: [filePath],
      );
      _log('从 play_history 表删除记录: $deleteFromHistory 行');

      // 3. 从所有播放列表中删除该文件
      final List<Map<String, dynamic>> playlists = await db.query('playlists');
      int updatedPlaylists = 0;

      for (var playlistMap in playlists) {
        try {
          final playlist = PlaylistModel.fromMap(playlistMap);
          final index =
              playlist.audioFiles.indexWhere((file) => file.path == filePath);

          if (index >= 0) {
            // 创建新的音频文件列表，排除要删除的文件
            final updatedAudioFiles =
                List<AudioFileModel>.from(playlist.audioFiles)..removeAt(index);

            // 更新播放列表
            final updatedPlaylist = playlist.copyWith(
              audioFiles: updatedAudioFiles,
              updatedAt: DateTime.now(),
            );

            // 保存到数据库
            await updatePlaylist(updatedPlaylist);
            updatedPlaylists++;
          }
        } catch (e) {
          _log('更新播放列表时出错: $e');
        }
      }

      _log('从 $updatedPlaylists 个播放列表中删除了文件');
      return deleteFromAudioFiles + deleteFromHistory + updatedPlaylists;
    } catch (e) {
      _log('删除音频文件失败: $e');
      rethrow;
    }
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
