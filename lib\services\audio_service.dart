import 'dart:io';
import 'dart:async'; // 添加Timer支持
import 'dart:math';
import 'package:flutter/foundation.dart'; // 导入foundation.dart获取kDebugMode
import 'package:audioplayers/audioplayers.dart' as audio_players;
import '../models/playlist_model.dart';
import '../database/database_helper.dart';
import 'lockscreen_service.dart'; // 导入锁屏服务
import 'package:flutter/services.dart'; // 导入MethodChannel

// 导入全局变量
import '../main.dart';

// 添加自定义日志函数，在非调试模式下不输出
import '../utils/logger.dart';

// 定义播放器状态枚举，用于替代just_audio中的ProcessingState
enum AudioProcessingState {
  idle,
  loading,
  ready,
  playing,
  paused,
  completed,
  error,
}

// 定义音频事件类型
enum AudioEventType {
  durationChanged, // 时长变更
  positionChanged, // 播放位置变更
  playStateChanged, // 播放状态变更
  playlistUpdated, // 播放列表更新
  playbackSpeedChanged, // 播放速度变更
}

// 音频事件类
class AudioEvent {
  final AudioEventType eventType;
  final String? audioFilePath;
  final Duration? duration;
  final Duration? position;
  final bool? isPlaying;
  final double? speed;

  AudioEvent({
    required this.eventType,
    this.audioFilePath,
    this.duration,
    this.position,
    this.isPlaying,
    this.speed,
  });
}

// 音频事件总线（采用单例模式）
class AudioEventBus {
  static final AudioEventBus _instance = AudioEventBus._internal();

  factory AudioEventBus() => _instance;

  AudioEventBus._internal();

  final StreamController<AudioEvent> _controller =
      StreamController<AudioEvent>.broadcast();

  // 发送事件
  void fire(AudioEvent event) {
    _controller.add(event);
  }

  // 获取事件流
  Stream<T> on<T>() {
    if (T == dynamic) {
      return _controller.stream as Stream<T>;
    } else {
      return _controller.stream.where((event) => event is T).cast<T>();
    }
  }

  // 释放资源
  void dispose() {
    _controller.close();
  }
}

// 定义一个类似PlayerState的类，便于迁移
class PlayerState {
  final bool playing;
  final AudioProcessingState processingState;

  PlayerState({required this.playing, required this.processingState});
}

// 使用audioplayers的PlayerState枚举
// 避免与我们自定义的PlayerState类混淆
final audio_players_stopped = audio_players.PlayerState.stopped;
final audio_players_playing = audio_players.PlayerState.playing;
final audio_players_paused = audio_players.PlayerState.paused;
final audio_players_completed = audio_players.PlayerState.completed;

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;

  // AudioPlayer 实例 - 从 late final 改为普通变量，允许重新创建
  audio_players.AudioPlayer? _audioPlayer;
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  bool _isInitialized = false;

  // 用于防止并发更新的锁
  final Set<String> _updatingFilePaths = {};

  // 记录正在计算时长的文件，避免重复计算
  final Set<String> _calculatingDurations = {};

  // 音频事件总线实例
  final AudioEventBus _audioEventBus = AudioEventBus();

  PlaylistModel? _currentPlaylist;
  int _currentIndex = 0;
  bool _isPlaying = false;
  double _playbackSpeed = 1.0;

  // 添加音频焦点相关变量
  bool _wasPlayingBeforeFocusLoss = false;
  bool _hasFocus = false;

  // 添加计时器，用于定期检查播放状态
  Timer? _advanceCheckTimer;
  bool _isAdvancingToNextTrack = false; // 防止重复切换

  // 添加用于检测播放位置停滞的变量
  Duration _lastPosition = Duration.zero;
  DateTime? _lastPositionTimestamp;

  // 添加一个变量存储音频时长，用于检测时长变化并纠正
  final Map<String, int> _initialDurations = {};

  // 添加一个额外的计时器检查是否卡住
  Timer? _positionStuckTimer;
  int _lastCheckedPosition = 0;
  int _stuckCheckCount = 0;

  // 添加变量用于跟踪已检查过时长的文件
  final Set<String> _durationCheckedFiles = {};

  // 添加用于锁屏控制更新节流的变量
  DateTime _lastLockscreenUpdateTime =
      DateTime.now().subtract(Duration(seconds: 10));

  // 添加用于模拟流的控制器
  final StreamController<PlayerState> _playerStateController =
      StreamController<PlayerState>.broadcast();
  final StreamController<Duration> _positionController =
      StreamController<Duration>.broadcast();
  final StreamController<Duration?> _durationController =
      StreamController<Duration?>.broadcast();

  // 添加方法通道，用于与原生平台通信
  static const _methodChannel =
      MethodChannel('com.example.audio/headphone_events');

  // 用于存储当前位置和时长的变量
  Duration _currentPosition = Duration.zero;
  Duration? _currentDuration;

  DateTime _lastDbUpdate = DateTime.now();
  DateTime _lastPositionUpdate = DateTime.now(); // 添加此变量跟踪位置更新时间

  // 使用延迟初始化，防止循环依赖
  LockscreenService? _lockscreenService;

  AudioService._internal() {
    // 初始化时订阅事件
    _audioEventBus.on<AudioEvent>().listen(_handleAudioEvent);

    // 设置方法通道监听
    _setupMethodChannelListener();

    // 初始化音频焦点监听
    _setupAudioFocusListener();
  }

  // 设置方法通道监听
  void _setupMethodChannelListener() {
    try {
      _methodChannel.setMethodCallHandler((call) async {
        try {
          _log('收到平台方法调用: ${call.method}');

          if (call.method == 'headphoneDisconnected') {
            _log('收到原生平台的耳机断开事件通知');
            await _handleHeadphoneDisconnect();
          } else if (call.method == 'audioFocusChanged') {
            // 处理音频焦点变化
            final bool hasFocus = call.arguments == 'gained';
            _handleAudioFocusChange(hasFocus);
          }
        } catch (e) {
          _log('处理平台方法调用失败: $e');
        }
        return null;
      });
    } catch (e) {
      _log('设置方法通道监听失败: $e');
    }
  }

  // 设置音频焦点监听
  void _setupAudioFocusListener() {
    try {
      // 调用原生平台方法注册音频焦点监听
      _methodChannel.invokeMethod('registerAudioFocusListener');
      _log('音频焦点监听器注册成功');
    } catch (e) {
      _log('注册音频焦点监听器失败: $e');
    }
  }

  // 处理音频焦点变化
  Future<void> _handleAudioFocusChange(bool hasFocus) async {
    try {
      _log('音频焦点变化: ${hasFocus ? "获得焦点" : "失去焦点"}');

      _hasFocus = hasFocus;

      if (!hasFocus) {
        // 失去焦点时，记录当前播放状态并暂停
        _wasPlayingBeforeFocusLoss = _isPlaying;
        if (_isPlaying && _isInitialized && _audioPlayer != null) {
          _log('因失去音频焦点而暂停播放，记录之前的播放状态: 播放中');
          await pause();
        }
      } else {
        // 重新获得焦点时，如果之前是播放状态，则恢复播放
        if (_wasPlayingBeforeFocusLoss &&
            _isInitialized &&
            _audioPlayer != null) {
          _log('重新获得音频焦点，恢复之前的播放状态');
          if (_currentPlaylist != null &&
              _currentIndex >= 0 &&
              _currentIndex < _currentPlaylist!.audioFiles.length) {
            await play();
          }
        }
      }
    } catch (e) {
      _log('处理音频焦点变化失败: $e');
    }
  }

  // 获取当前播放列表的getter
  PlaylistModel? get currentPlaylist => _currentPlaylist;

  // 获取当前索引的getter
  int get currentIndex => _currentIndex;

  // 获取当前播放状态的getter
  bool get isPlaying => _isPlaying;

  // 获取当前播放速度的getter
  double get playbackSpeed => _playbackSpeed;

  // 获取AudioPlayer实例的getter
  audio_players.AudioPlayer? get audioPlayer => _audioPlayer;

  bool get isInitialized => _isInitialized;
  Duration get currentPosition =>
      _isInitialized ? _currentPosition : Duration.zero;
  Duration get duration =>
      _isInitialized ? (_currentDuration ?? Duration.zero) : Duration.zero;

  // 使用模拟流提供与just_audio兼容的API
  Stream<PlayerState> get playerStateStream => _playerStateController.stream;
  Stream<Duration> get positionStream => _positionController.stream;
  Stream<Duration?> get durationStream => _durationController.stream;

  Future<void> init() async {
    if (_isInitialized) {
      _log('AudioService 已经初始化，跳过');
      return;
    }

    try {
      // 先确保数据库已初始化
      await _databaseHelper.database;

      // 释放以前的音频播放器资源（如果存在）
      try {
        if (_audioPlayer != null) {
          await _audioPlayer!.dispose();
          _log('释放旧的AudioPlayer资源');
          _audioPlayer = null; // 明确设置为 null
        }
      } catch (e) {
        // 忽略错误，因为我们要创建新的实例
        _log('释放旧AudioPlayer资源时出错（可忽略）: $e');
        _audioPlayer = null; // 强制为 null，确保可以创建新实例
      }

      // 初始化 AudioPlayer
      try {
        _audioPlayer = audio_players.AudioPlayer();
        _log('成功创建新的 AudioPlayer 实例');
      } catch (e) {
        _log('创建 AudioPlayer 出错: $e');
        // 确保错误被正确传播
        throw Exception('无法初始化音频播放器: $e');
      }

      // 设置全局后台播放标志为false
      isBackgroundAudioInitialized = false;

      // 配置 AudioPlayer
      await _audioPlayer!
          .setReleaseMode(audio_players.ReleaseMode.stop); // 播放完停止释放资源

      // 添加耳机断开事件监听
      try {
        _log('注册耳机断开事件监听');

        // 使用onPlayerStateChanged事件监听，因为耳机断开时可能触发状态变化
        _audioPlayer!.onPlayerStateChanged.listen((state) {
          _log('播放器状态变化可能是由于耳机断开: $state');

          // 检查是否是因为耳机断开导致的播放停止
          if (_isPlaying && state == audio_players.PlayerState.paused) {
            // 可能是由于耳机断开导致的暂停
            // 我们会在这里处理耳机断开的情况，保存位置并确保UI更新
            _handleHeadphoneDisconnect();
          }
        });
      } catch (e) {
        _log('注册耳机断开事件监听失败: $e');
      }

      // 设置播放状态变化监听
      _audioPlayer!.onPlayerStateChanged.listen((state) {
        _log('播放器状态变更: $state');

        final wasPlaying = _isPlaying;
        _isPlaying = state == audio_players_playing;

        // 播放状态转换
        AudioProcessingState processingState;

        if (state == audio_players_stopped) {
          processingState = AudioProcessingState.idle;
        } else if (state == audio_players_playing) {
          processingState = AudioProcessingState.playing;
        } else if (state == audio_players_paused) {
          processingState = AudioProcessingState.paused;
        } else if (state == audio_players_completed) {
          processingState = AudioProcessingState.completed;
          // 当状态为已完成时，确保更新完成状态
          _log('检测到播放状态为已完成');
          // 异步等待更新完成，避免阻塞主线程
          Logger.warning('检测到播放状态为已完成');
          _updateAudioFileCompleted().then((_) {
            // 更新完成后，确保发送播放列表更新事件
            if (_currentPlaylist != null &&
                _currentIndex >= 0 &&
                _currentIndex < _currentPlaylist!.audioFiles.length) {
              final fileName =
                  _currentPlaylist!.audioFiles[_currentIndex].fileName;
              Logger.warning('播放状态为已完成的更新处理完成，文件名: $fileName，发送播放列表更新事件');
              _audioEventBus.fire(AudioEvent(
                eventType: AudioEventType.playlistUpdated,
                audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
              ));
              // 等待短暂时间，确保UI更新和数据库操作完成
              //await Future.delayed(Duration(milliseconds: 500));
              _log('准备播放下一首...');
              _isPlaying = true;
              _playNext();
            }
          });
        } else {
          processingState = AudioProcessingState.idle;
        }

        // 广播状态变化
        _playerStateController.add(PlayerState(
          playing: _isPlaying,
          processingState: processingState,
        ));

        // 发送播放状态变化事件
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.playStateChanged,
          isPlaying: _isPlaying,
          audioFilePath: _currentPlaylist?.audioFiles.isNotEmpty == true &&
                  _currentIndex >= 0 &&
                  _currentIndex < _currentPlaylist!.audioFiles.length
              ? _currentPlaylist!.audioFiles[_currentIndex].path
              : null,
        ));

        // 当播放状态变化时（特别是从播放变为暂停时），保存当前位置
        if (wasPlaying && !_isPlaying) {
          _log('播放状态从播放变为暂停，保存当前位置');
          _saveCurrentAudioPosition();
          _stopAdvanceCheckTimer(); // 停止定期检查
        }

        // 当开始播放时，启动计时器检查
        if (_isPlaying && !wasPlaying) {
          _log('播放状态从暂停变为播放，启动计时器');
          // 重置位置更新时间戳，确保开始播放后能立即记录第一个位置
          _lastPositionUpdate = DateTime.now().subtract(Duration(seconds: 5));
          _startAdvanceCheckTimer(); // 启动定期检查
        }
      });

      // 监听播放位置变化
      _audioPlayer!.onPositionChanged.listen((position) {
        _currentPosition = position;

        // 广播位置更新
        _positionController.add(position);

        // 发送播放位置变化事件
        if (_currentPlaylist != null &&
            _currentIndex >= 0 &&
            _currentIndex < _currentPlaylist!.audioFiles.length) {
          _audioEventBus.fire(AudioEvent(
            eventType: AudioEventType.positionChanged,
            audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
            position: position,
          ));
        }

        if (_currentPlaylist != null &&
            _currentPlaylist!.audioFiles.isNotEmpty) {
          // 添加节流逻辑，每3秒才更新一次数据库位置
          // 这可以显著减少数据库写入次数，提高性能并降低资源消耗
          // 重要的位置变化（如暂停/拖动进度条）仍然会通过forceUpdate立即保存
          final now = DateTime.now();
          final timeSinceLastPositionUpdate =
              now.difference(_lastPositionUpdate).inSeconds;

          // 只有当播放状态为播放中，且距离上次更新超过3秒时，才调用更新函数
          if (_isPlaying && timeSinceLastPositionUpdate >= 3) {
            _log('位置更新节流: 3秒间隔后更新数据库位置，当前位置: ${position.inSeconds}秒');
            _updateAudioFilePosition(position.inSeconds);
            _lastPositionUpdate = now; // 更新时间戳
          }
        }
      });

      // 监听播放时长变化
      _audioPlayer!.onDurationChanged.listen((duration) {
        _currentDuration = duration;

        // 广播时长更新
        _durationController.add(duration);

        // 发送时长变化事件
        if (_currentPlaylist != null &&
            _currentIndex >= 0 &&
            _currentIndex < _currentPlaylist!.audioFiles.length) {
          final currentFile = _currentPlaylist!.audioFiles[_currentIndex];
          _audioEventBus.fire(AudioEvent(
            eventType: AudioEventType.durationChanged,
            audioFilePath: currentFile.path,
            duration: duration,
          ));
        }

        if (duration != Duration.zero &&
            _currentPlaylist != null &&
            _currentIndex >= 0 &&
            _currentIndex < _currentPlaylist!.audioFiles.length) {
          final currentFile = _currentPlaylist!.audioFiles[_currentIndex];

          // 如果该文件已经检查过时长，则跳过
          if (_durationCheckedFiles.contains(currentFile.path)) {
            return;
          }

          // 标记该文件已检查过时长
          _durationCheckedFiles.add(currentFile.path);

          // 记录初始时长（如果还没有记录）并持久化存储
          if (duration.inSeconds > 0) {
            bool isNewDuration = false;

            if (!_initialDurations.containsKey(currentFile.path)) {
              isNewDuration = true;
              _initialDurations[currentFile.path] = duration.inSeconds;
              _log('记录文件初始时长: ${currentFile.fileName}, ${duration.inSeconds}秒');

              // 持久化保存到数据库
              _saveInitialDuration(currentFile.path, duration.inSeconds);

              // 立即使用新方法更新和存储时长
              updateAndStoreAudioDuration(currentFile.path, duration);
            }

            // 检测时长变化超过3%并纠正
            if (!isNewDuration &&
                _initialDurations.containsKey(currentFile.path)) {
              final initialDuration = _initialDurations[currentFile.path]!;
              final currentDuration = duration.inSeconds;

              // 计算变化百分比
              final percentChange = initialDuration > 0
                  ? ((currentDuration - initialDuration).abs() /
                      initialDuration)
                  : 0;

              // 只在播放开始时记录一次时长变化，避免重复日志
              if (percentChange > 0.03) {
                _log(
                    '检测到文件时长变化: ${currentFile.fileName}, 初始: ${initialDuration}秒, 当前: ${currentDuration}秒, 变化: ${(percentChange * 100).toStringAsFixed(1)}%');

                // 时长有显著变化，更新存储
                if (currentDuration > initialDuration) {
                  _log('新时长更大，更新存储');
                  updateAndStoreAudioDuration(currentFile.path, duration);
                }

                // 更新音频模型中的时长为初始记录的时长，确保一致性
                _updateCurrentAudioFileDuration(initialDuration);
              }
            }
          }
        }
      });

      // 设置卡住检测定时器
      _setupStuckDetectionTimer();

      // 完全初始化基本的音频播放器功能后，再初始化锁屏控制服务
      _isInitialized = true;
      _log('基本音频服务初始化完成');

      // 初始化锁屏控制服务
      try {
        // 在这里初始化锁屏服务，并传入当前实例
        _lockscreenService = LockscreenService(this);
        await _lockscreenService!.init();
        _log('锁屏控制服务初始化成功');
      } catch (e) {
        _log('锁屏控制服务初始化失败: $e');
        // 锁屏服务初始化失败不影响主要功能，继续初始化
      }

      // 初始化音频焦点管理
      try {
        _setupAudioFocusListener();
        _hasFocus = true; // 默认假设初始化时有焦点
        _log('音频焦点管理初始化成功');
      } catch (e) {
        _log('音频焦点管理初始化失败: $e');
        // 音频焦点初始化失败不影响主要功能，继续初始化
      }

      _log('AudioService 完全初始化完成');
    } catch (e) {
      _log('AudioService 初始化失败: $e');
      _isInitialized = false;
      rethrow;
    }
  }

  Future<bool> loadPlaylist(PlaylistModel playlist,
      {int startIndex = 0}) async {
    // 确保 AudioService 已初始化
    if (!_isInitialized) {
      await init();
    }

    // 清空初始时长记录，准备重新记录
    _initialDurations.clear();

    // 首先检查是否已经加载了同一个播放列表，但文件数量不同，这可能意味着有新文件被添加
    final hasNewFiles = _currentPlaylist != null &&
        _currentPlaylist!.id == playlist.id &&
        _currentPlaylist!.audioFiles.length != playlist.audioFiles.length;

    // 如果播放列表已经更新，保存当前播放的进度信息
    if (hasNewFiles) {
      await _saveCurrentAudioPosition();
    }

    // 保存原始播放速度设置
    final oldSpeed = _playbackSpeed;

    // 先从参数中获取播放列表
    _currentPlaylist = playlist;
    _log(
        '加载播放列表: ${playlist.name}, ID: ${playlist.id}, 速度: ${playlist.playbackSpeed}');

    // 如果播放列表有ID，尝试从数据库重新获取最新数据
    if (playlist.id != null) {
      try {
        final freshPlaylist =
            await _databaseHelper.getPlaylistById(playlist.id!);
        if (freshPlaylist != null) {
          _log(
              '从数据库获取最新播放列表数据: ${freshPlaylist.name}, 速度: ${freshPlaylist.playbackSpeed}');
          // 保留原始的音频文件列表，但使用数据库中的其他属性
          _currentPlaylist = freshPlaylist.copyWith(
            audioFiles: playlist.audioFiles,
          );
          _log(
              '更新后的播放列表: ${_currentPlaylist!.name}, 速度: ${_currentPlaylist!.playbackSpeed}');
        }
      } catch (e) {
        _log('从数据库获取最新播放列表数据失败: $e');
      }
    }

    // 从播放列表加载播放速度设置
    _loadPlaybackSpeedFromPlaylist();

    // 如果速度变化了，记录日志
    if (oldSpeed != _playbackSpeed) {
      _log('播放速度已更新: $oldSpeed -> $_playbackSpeed');
    }

    // 记录每个文件的初始时长
    for (var file in _currentPlaylist!.audioFiles) {
      if (file.durationInSeconds > 0) {
        _initialDurations[file.path] = file.durationInSeconds;
      }
    }

    // 从数据库加载初始时长记录，补充内存中没有的数据
    await _loadInitialDurationsFromDatabase();

    // 播放列表为空时，直接返回
    if (playlist.audioFiles.isEmpty) {
      _log('警告: 播放列表为空');
      return false;
    }

    // 重新初始化当前索引
    // 首先检查播放列表的 currentPlayingIndex 字段
    int initialIndex = playlist.currentPlayingIndex;

    // 如果指定了startIndex参数且有效，则优先使用它
    if (startIndex >= 0 && startIndex < playlist.audioFiles.length) {
      initialIndex = startIndex;
    }

    // 确保索引在有效范围内
    if (initialIndex < 0 || initialIndex >= playlist.audioFiles.length) {
      initialIndex = 0;
    }

    // 使用新方法更新当前索引
    await _updateCurrentPlayingIndex(initialIndex);

    bool success = false;
    int retryCount = 0;
    int maxRetries = 3;

    while (!success && retryCount < maxRetries) {
      try {
        retryCount++;
        if (retryCount > 1) {
          _log('正在尝试第 $retryCount 次加载播放列表...');
          // 在重试时增加延迟
          await Future.delayed(Duration(milliseconds: 300 * retryCount));
        }

        // 验证所有音频文件是否存在
        final validAudioFiles = <AudioFileModel>[];
        for (final audioFile in playlist.audioFiles) {
          final file = File(audioFile.path);
          if (file.existsSync()) {
            validAudioFiles.add(audioFile);
          } else {
            _log('警告: 文件不存在: ${audioFile.path}');
          }
        }

        // 更新播放列表，只包含有效文件
        if (validAudioFiles.isEmpty) {
          _log('错误: 播放列表中没有有效的音频文件');
          throw Exception('播放列表中没有有效的音频文件');
        }

        _currentPlaylist =
            _currentPlaylist!.copyWith(audioFiles: validAudioFiles);
        if (_currentIndex >= validAudioFiles.length) {
          _currentIndex = 0;
        }

        // 加载第一个音频文件但不自动播放
        await _loadCurrentAudio();
        success = true;

        // 如果有上一次的播放位置，恢复它
        if (success) {
          try {
            final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
            int lastPosition = audioFile.lastPositionInSeconds;

            // 如果内存中没有记录，则从数据库获取
            if (lastPosition <= 0) {
              lastPosition = await _databaseHelper.getLastPositionForAudio(
                  audioFile.path,
                  playlistId: _currentPlaylist!.id);
              _log('从数据库获取上次位置: $lastPosition 秒');
            } else {
              _log('使用内存中的上次位置: $lastPosition 秒');
            }

            if (lastPosition > 0) {
              // 确保有足够的延迟使音频播放器加载完成
              await Future.delayed(const Duration(milliseconds: 300));

              // 确保不超过文件总长度
              final duration = _currentDuration;
              if (duration != null && lastPosition > duration.inSeconds) {
                lastPosition = duration.inSeconds - 5; // 留5秒余量
                _log('调整位置以不超过文件长度: $lastPosition 秒');
              }

              await _audioPlayer!.seek(Duration(seconds: lastPosition));
              _log('播放列表加载后恢复播放位置到 $lastPosition 秒');
            }
          } catch (e) {
            _log('恢复播放位置失败: $e');
          }
        }

        _log('播放列表加载' +
            (success ? '成功' : '失败') +
            '，共 ${validAudioFiles.length} 个音频文件');
      } catch (e) {
        _log('加载播放列表失败 (尝试 $retryCount/$maxRetries): $e');
        if (retryCount >= maxRetries) {
          _log('达到最大重试次数，放弃加载播放列表');
          // 出错时不要抛出异常，只是记录错误
        }
      }
    }

    // 更新锁屏控制
    await _updateLockscreenControls();

    _log('播放列表加载完成: ${_currentPlaylist!.name}, 当前索引: $_currentIndex');
    return success; // 返回加载是否成功
  }

  Future<void> _loadCurrentAudio() async {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      return;
    }

    if (!_isInitialized) {
      _log('警告: 尝试加载音频时 AudioPlayer 未初始化');
      await init();
      // 如果初始化后仍未成功，则返回
      if (!_isInitialized) return;
    }

    try {
      final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
      final file = File(audioFile.path);
      if (!file.existsSync()) {
        _log('错误: 文件不存在: ${audioFile.path}');
        return;
      }

      // 获取上次播放位置 - 首先查看内存中的记录
      int lastPosition = audioFile.lastPositionInSeconds;

      // 如果内存中没有记录，则从数据库获取
      if (lastPosition <= 0) {
        lastPosition = await _databaseHelper.getLastPositionForAudio(
            audioFile.path,
            playlistId: _currentPlaylist!.id);
        _log('从数据库获取上次位置: $lastPosition 秒');
      } else {
        _log('使用内存中的上次位置: $lastPosition 秒');
      }

      // 先停止当前播放
      await _audioPlayer!.stop();

      // 设置音频源
      final source = audio_players.DeviceFileSource(file.path);
      await _audioPlayer!.setSource(source);

      _log('成功设置音频源: ${file.path}');

      // 延迟一段时间确保音频加载完成
      await Future.delayed(const Duration(milliseconds: 200));

      // 确保不超过文件总长度
      if (_currentDuration != null &&
          lastPosition > _currentDuration!.inSeconds) {
        lastPosition = _currentDuration!.inSeconds;
        _log('调整位置以不超过文件长度: $lastPosition 秒');
      }

      // 设置播放位置
      if (lastPosition > 0) {
        await _audioPlayer!.seek(Duration(seconds: lastPosition));
        _log('定位到上次位置: $lastPosition 秒');
      }

      // 设置播放速度
      await _audioPlayer!.setPlaybackRate(_playbackSpeed);

      // 确保播放器不是处于stopped状态，而是进入暂停状态
      await _audioPlayer!.pause();
      _log('加载完成，设置为暂停状态');
    } catch (e) {
      _log('加载当前音频失败: $e');
      // 出错时不要抛出异常，只是记录错误
    }
  }

  /// 播放指定索引的音频文件
  Future<bool> play([int? index]) async {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      _log('无法播放: 播放列表为空');
      return false;
    }
    Logger.info('play: $index');
    // 如果指定了索引且有效，则使用指定索引；否则使用当前索引
    final targetIndex = (index != null &&
            index >= 0 &&
            index < _currentPlaylist!.audioFiles.length)
        ? index
        : _currentIndex;

    if (targetIndex < 0 || targetIndex >= _currentPlaylist!.audioFiles.length) {
      _log('无法播放: 索引 $targetIndex 超出范围');
      return false;
    }

    // 更新当前索引
    _currentIndex = targetIndex;

    try {
      _log(
          '播放索引: $_currentIndex, 文件: ${_currentPlaylist!.audioFiles[_currentIndex].fileName}');

      // 确保文件存在
      final filePath = _currentPlaylist!.audioFiles[_currentIndex].path;
      if (!await File(filePath).exists()) {
        _log('文件不存在: $filePath');
        return false;
      }

      // 开始播放
      await _audioPlayer!.resume();
      _isPlaying = true;

      // 发送播放状态变化事件
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playStateChanged,
        isPlaying: true,
        audioFilePath: filePath,
      ));

      // 启动定期检查
      _startAdvanceCheckTimer();

      // 移除直接调用锁屏更新，通过事件系统触发
      return true;
    } catch (e) {
      _log('播放失败: $e');

      // 错误处理 - 自动跳到下一首但不更新当前文件状态
      if (e.toString().contains('MEDIA_ERROR_UNKNOWN') ||
          e.toString().contains('PlatformException')) {
        _log('检测到播放器错误，暂停播放');

        // 直接调用 pause()，让 onPlayerStateChanged 处理状态更新
        await _audioPlayer!.pause();
      }

      return false;
    }
  }

  // 暂停播放
  Future<void> pause() async {
    if (!_isInitialized || _audioPlayer == null) {
      _log('暂停失败: 播放器未初始化');
      return;
    }

    try {
      _log('1. 暂停播放，当前是否播放: $_isPlaying');
      if (_isPlaying) {
        _log('2. 正在播放状态，先保存位置');
        await _saveCurrentAudioPosition();

        _log('6. 现在暂停播放器');
        await _audioPlayer!.pause();
        _log('7. 播放器已暂停');
      } else {
        _log('3. 当前未在播放状态，直接暂停播放器');
        await _audioPlayer!.pause();
        _log('4. 播放器已暂停');
      }

      _isPlaying = false;
      _stopAdvanceCheckTimer();

      // 发送播放状态变化事件
      if (_currentPlaylist != null &&
          _currentIndex >= 0 &&
          _currentIndex < _currentPlaylist!.audioFiles.length) {
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.playStateChanged,
          isPlaying: false,
          audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
        ));
      }

      // 移除直接调用锁屏控制更新代码，通过事件系统触发
    } catch (e) {
      _log('暂停播放失败: $e');
    }
  }

  // 停止播放
  Future<void> stop() async {
    if (!_isInitialized || _audioPlayer == null) {
      _log('停止失败: 播放器未初始化');
      return;
    }

    try {
      // 保存当前播放位置
      await _saveCurrentAudioPosition();
      await _audioPlayer!.stop();

      // 更新锁屏控制
      if (_lockscreenService != null) {
        await _lockscreenService!.updatePlaybackState(isPlaying: false);
      }
    } catch (e) {
      _log('停止播放失败: $e');
    }

    _isPlaying = false;
    _stopAdvanceCheckTimer();
  }

  Future<void> seek(Duration position) async {
    if (!_isInitialized) return;

    try {
      await _audioPlayer!.seek(position);
      // 在结束寻址后更新存储的位置
      if (position.inSeconds > 0) {
        _log('seek操作: 强制更新播放位置到 ${position.inSeconds}秒');
        // 强制更新位置，忽略节流逻辑
        await _updateAudioFilePosition(position.inSeconds, forceUpdate: true);
        // 更新时间戳，避免恢复播放时重复保存
        _lastPositionUpdate = DateTime.now();
      }
    } catch (e) {
      _log('seek 操作失败: $e');
    }
  }

  Future<void> setPlaybackSpeed(double speed) async {
    if (!_isInitialized || _audioPlayer == null) {
      _log('未初始化，无法设置播放速度');
      return;
    }

    try {
      // 确保速度在有效范围内
      double validSpeed = speed;

      // 支持的速度: 0.5, 0.75, 1.0, 1.1, 1.2, 1.5, 2.0
      // 如果设置了不支持的速度，则使用最接近的支持值
      const List<double> supportedSpeeds = [
        0.5,
        0.75,
        1.0,
        1.1,
        1.2,
        1.25,
        1.5,
        1.75,
        2.0
      ];
      if (!supportedSpeeds.contains(speed)) {
        supportedSpeeds
            .sort((a, b) => (a - speed).abs().compareTo((b - speed).abs()));
        validSpeed = supportedSpeeds.first;
        _log('调整到最接近的支持速度: $validSpeed');
      }

      _log('设置播放速度: $validSpeed');
      await _audioPlayer!.setPlaybackRate(validSpeed);
      _playbackSpeed = validSpeed;
      _log('播放器速度已更新为: $validSpeed');

      // 如果当前有播放列表，将速度保存到播放列表中
      if (_currentPlaylist != null) {
        _log('当前播放列表: ${_currentPlaylist!.name}, ID: ${_currentPlaylist!.id}');

        if (_currentPlaylist!.id != null) {
          // 更新内存中的播放列表
          final oldSpeed = _currentPlaylist!.playbackSpeed;
          _currentPlaylist = _currentPlaylist!.copyWith(
            playbackSpeed: validSpeed,
            updatedAt: DateTime.now(),
          );
          _log(
              '内存中的播放列表速度已更新: $oldSpeed -> ${_currentPlaylist!.playbackSpeed}');

          // 更新数据库中的播放列表
          try {
            _log('准备更新数据库中的播放速度...');

            // 使用DatabaseHelper实例直接更新播放列表，确保所有字段正确映射
            final updateResult =
                await _databaseHelper.updatePlaylist(_currentPlaylist!);

            _log('数据库更新结果: 影响的行数=$updateResult');

            // 验证更新是否成功
            final updatedPlaylist =
                await _databaseHelper.getPlaylistById(_currentPlaylist!.id!);
            if (updatedPlaylist != null) {
              _log('验证: 数据库中的速度现在是 ${updatedPlaylist.playbackSpeed}');

              // 如果数据库中的值与预期不符，尝试使用原始SQL更新
              if (updatedPlaylist.playbackSpeed != validSpeed) {
                _log('警告: 数据库中的速度与预期不符，尝试直接SQL更新');

                final db = await _databaseHelper.database;
                final directUpdateResult = await db.rawUpdate(
                    'UPDATE playlists SET playback_speed = ?, updatedAt = ? WHERE id = ?',
                    [
                      validSpeed,
                      DateTime.now().millisecondsSinceEpoch,
                      _currentPlaylist!.id
                    ]);

                _log('直接SQL更新结果: 影响的行数=$directUpdateResult');
              }
            }
          } catch (e) {
            _log('保存播放速度到数据库失败: $e');
            // 尝试使用备用方法更新
            try {
              final db = await _databaseHelper.database;
              final backupUpdateResult = await db.update(
                'playlists',
                {
                  'playback_speed': validSpeed,
                  'updatedAt': DateTime.now().millisecondsSinceEpoch
                },
                where: 'id = ?',
                whereArgs: [_currentPlaylist!.id],
              );
              _log('备用更新方法结果: 影响的行数=$backupUpdateResult');
            } catch (backupError) {
              _log('备用更新方法也失败: $backupError');
            }
          }
        } else {
          _log('警告: 当前播放列表没有ID，无法保存到数据库');
        }
      } else {
        _log('警告: 当前没有播放列表，速度只在内存中更新');
      }

      // 广播播放速度变更事件，确保UI可以更新
      _log('广播播放速度变更事件: $validSpeed');
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playbackSpeedChanged,
        speed: validSpeed,
      ));
    } catch (e) {
      _log('设置播放速度失败: $e');
    }
  }

  // 修改返回类型为Future<bool>
  Future<bool> playAtIndex(int index, {bool autoPlay = true}) async {
    if (_currentPlaylist == null ||
        index < 0 ||
        index >= _currentPlaylist!.audioFiles.length) {
      return false;
    }

    if (!_isInitialized) {
      _log('警告: 尝试播放指定索引时 AudioPlayer 未初始化');
      await init();
      // 如果初始化后仍未成功，则返回
      if (!_isInitialized) return false;
    }

    try {
      // 保存当前播放文件的进度
      if (_currentIndex != index) {
        // 无论是否正在播放，都保存当前位置
        await _saveCurrentAudioPosition();
      }

      // 先更新当前索引，确保界面显示正确的音频文件 - 使用新方法
      await _updateCurrentPlayingIndex(index);

      // 获取目标音频文件的上次播放位置
      final audioFile = _currentPlaylist!.audioFiles[index];
      int lastPosition = audioFile.lastPositionInSeconds;

      // 如果内存中没有记录，则从数据库获取
      if (lastPosition <= 0) {
        lastPosition = await _databaseHelper.getLastPositionForAudio(
            audioFile.path,
            playlistId: _currentPlaylist!.id);
        _log('从数据库获取上次位置: $lastPosition 秒');
      } else {
        _log('使用内存中的上次位置: $lastPosition 秒');
      }
      Logger.warning('play2: $index');
      Logger.warning('playAtIndex: $lastPosition');
      // 创建播放位置
      final position =
          lastPosition > 0 ? Duration(seconds: lastPosition) : Duration.zero;

      // 检查文件是否存在
      final file = File(audioFile.path);
      if (!await file.exists()) {
        _log('文件不存在: ${audioFile.path}');
        return false;
      }

      // 停止当前播放
      await _audioPlayer!.stop();
      // 设置新的音频源
      final source = audio_players.DeviceFileSource(file.path);
      await _audioPlayer!.setSource(source);

      // 延迟一段时间确保seek完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 恢复到上次位置
      if (position.inSeconds > 0) {
        try {
          await _audioPlayer!.seek(position);
          _log('定位到上次位置: ${position.inSeconds} 秒');
          Logger.warning('定位到上次位置: ${position.inSeconds} 秒');
        } catch (e) {
          _log('定位失败，将从头开始播放: $e');
        }
      }
      Logger.warning('autoPlay: $autoPlay');
      if (autoPlay) {
        // 开始播放
        await _audioPlayer!.resume();
        _isPlaying = true;
        _log('自动播放已开启，开始播放');

        Logger.warning('自动播放已开启，开始播放');
        // 发送播放状态变化事件
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.playStateChanged,
          isPlaying: true,
          audioFilePath: audioFile.path,
        ));
      } else {
        // 当不自动播放时，主动将播放器状态设置为暂停状态
        await _audioPlayer!.pause();
        _isPlaying = false;
        _log('自动播放已关闭，设置为暂停状态');

        // 发送播放状态变化事件
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.playStateChanged,
          isPlaying: false,
          audioFilePath: audioFile.path,
        ));
      }

      _log(
          '${autoPlay ? "播放" : "准备"} 索引 $index 文件 ${audioFile.fileName}，从位置 ${position.inSeconds} 秒开始');

      return true;
    } catch (e) {
      _log('播放指定索引失败: $e');

      // 错误处理 - 自动跳到下一首但不更新当前文件状态
      if (e.toString().contains('MEDIA_ERROR_UNKNOWN') ||
          e.toString().contains('PlatformException')) {
        _log('检测到播放器错误，暂停播放');

        // 直接调用 pause()，让 onPlayerStateChanged 处理状态更新
        await _audioPlayer!.pause();
      }

      return false;
    }
  }

  // 添加准备指定索引的音频但不自动播放的方法
  Future<void> prepareAtIndex(int index, {bool autoPlay = false}) async {
    if (_currentPlaylist == null ||
        index < 0 ||
        index >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    if (!_isInitialized) {
      _log('警告: 尝试准备指定索引时 AudioPlayer 未初始化');
      await init();
      // 如果初始化后仍未成功，则返回
      if (!_isInitialized) return;
    }

    try {
      // 保存当前播放文件的进度
      if (_currentIndex != index) {
        // 无论是否正在播放，都保存当前位置
        await _saveCurrentAudioPosition();
      }

      // 先更新当前索引，确保界面显示正确的音频文件 - 使用新方法
      await _updateCurrentPlayingIndex(index);

      // 获取目标音频文件的上次播放位置
      final audioFile = _currentPlaylist!.audioFiles[index];
      int lastPosition = audioFile.lastPositionInSeconds;

      // 如果内存中没有记录，则从数据库获取
      if (lastPosition <= 0) {
        lastPosition = await _databaseHelper.getLastPositionForAudio(
            audioFile.path,
            playlistId: _currentPlaylist!.id);
      }

      // 创建播放位置
      final position =
          lastPosition > 0 ? Duration(seconds: lastPosition) : Duration.zero;

      // 检查文件是否存在
      final file = File(audioFile.path);
      if (!file.existsSync()) {
        _log('错误: 文件不存在: ${audioFile.path}');
        throw Exception('文件不存在: ${audioFile.path}');
      }

      // 停止当前播放
      await _audioPlayer!.stop();

      // 设置新的音频源
      final source = audio_players.DeviceFileSource(file.path);
      await _audioPlayer!.setSource(source);

      // 短暂延迟让资源准备
      await Future.delayed(const Duration(milliseconds: 200));

      // 尝试定位到指定位置
      if (position.inSeconds > 0) {
        await _audioPlayer!.seek(position);
      }

      // 只有在 autoPlay 为 true 时才自动播放
      if (autoPlay) {
        _log('准备完成，开始播放');
        await _audioPlayer!.resume();
        _isPlaying = true;
      } else {
        // 当不自动播放时，主动将播放器状态设置为暂停状态
        // 这样可以避免播放器保持在stopped状态，UI显示为一直加载
        await _audioPlayer!.pause();
        _log('准备完成，设置为暂停状态');
      }

      _log(
          '准备索引 $index 文件 ${audioFile.fileName}，位置设置为 ${position.inSeconds} 秒');
    } catch (e) {
      _log('准备指定索引失败: $e');
      // 尝试重新初始化和加载播放列表
      try {
        _log('尝试重新初始化播放器并加载播放列表');
        _isInitialized = false;
        await init();
        if (_currentPlaylist != null) {
          await loadPlaylist(_currentPlaylist!, startIndex: index);

          // 如果需要自动播放
          if (autoPlay) {
            await _audioPlayer!.resume();
            _isPlaying = true;
          } else {
            // 即使重新初始化后，也确保状态为暂停而不是停止
            await _audioPlayer!.pause();
          }
        }
      } catch (reinitError) {
        _log('重新初始化播放器失败: $reinitError');
      }
    }
  }

  // 保存当前播放位置的方法
  Future<void> saveCurrentAudioPosition() async {
    // 如果没有实际播放，则跳过更新位置
    if (!_isPlaying) {
      _log('5. 未在播放状态，跳过保存位置');
      _log('------ 保存播放位置过程完成（未播放不保存） ------');
      return;
    }

    if (_currentPlaylist == null ||
        _currentPlaylist!.audioFiles.isEmpty ||
        _currentIndex >= _currentPlaylist!.audioFiles.length ||
        !_isInitialized) {
      _log('无法保存位置: 播放列表为空或索引无效');
      return;
    }

    try {
      _log('------ 开始保存播放位置过程 ------');
      // 获取当前播放位置
      final position = _currentPosition;
      final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
      _log('1. 当前音频: ${audioFile.fileName}');
      _log('2. 播放列表ID: ${_currentPlaylist?.id}');
      _log('3. 当前播放位置: ${position.inSeconds}秒');
      _log('4. 当前是否播放: ${_isPlaying ? "是" : "否"}');

      // 如果当前没有有效的播放位置，但文件有保存的位置，则使用保存的位置
      // 这样确保我们不会因为临时的播放位置为0而丢失已经保存的播放进度
      int positionInSeconds = position.inSeconds;
      if (positionInSeconds <= 0 && audioFile.lastPositionInSeconds > 0) {
        positionInSeconds = audioFile.lastPositionInSeconds;
        _log('5. 位置为0，使用上次保存的位置: ${positionInSeconds}秒');
      }

      // 仅当位置有效时保存，避免覆盖有效的历史记录
      if (positionInSeconds > 0) {
        _log('6. 调用_updateAudioFilePosition更新位置');
        await _updateAudioFilePosition(positionInSeconds);
        _log('7. 位置已成功更新到数据库');
      } else {
        _log('6. 位置无效(${positionInSeconds}秒)，跳过保存');
      }
      _log('------ 保存播放位置过程完成 ------');
    } catch (e) {
      _log('保存播放位置失败: $e');
    }
  }

  // 内部版本的保存方法，用于私有调用
  Future<void> _saveCurrentAudioPosition() async {
    return saveCurrentAudioPosition();
  }

  // 播放下一个音频文件
  Future<void> _playNext() async {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      _log('播放列表为空，无法播放下一首');
      return;
    }

    try {
      // 防止重复触发下一曲
      if (_isAdvancingToNextTrack) {
        _log('已经在切换到下一首，忽略重复请求');
        return;
      }

      // 设置标志，防止重复进入
      _isAdvancingToNextTrack = true;

      // 保存当前播放位置，重要!
      await _saveCurrentAudioPosition();

      // 执行重置操作，清除检查标记
      await _resetPlayback();

      // 寻找下一个未完成的索引
      final nextIndex = _findNextUncompletedIndex();

      // 记录当前是否正在播放
      bool wasPlaying = _isPlaying;
      _log('切换前的播放状态: ${wasPlaying ? "播放中" : "已暂停"}');

      // 在清除当前播放项之前，先预先通知锁屏控制器音乐已暂停
      if (_lockscreenService != null) {
        _log('先通知锁屏控制器暂停播放，然后再切换音轨');
        await _lockscreenService!.updatePlaybackState(isPlaying: false);
      }

      // 在切换前先发送播放状态变更事件
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playStateChanged,
        isPlaying: false,
        audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
      ));

      // 短暂延迟确保暂停状态已更新
      await Future.delayed(Duration(milliseconds: 50));

      if (nextIndex >= 0) {
        _log('找到下一个未完成的音频，索引: $nextIndex');
        await playAtIndex(nextIndex, autoPlay: wasPlaying);
      } else {
        // 如果没有找到未完成的音频，则从头开始播放
        _log('没有找到未完成的音频，从第一个开始播放');
        // 从列表头部重新开始
        if (_currentPlaylist!.audioFiles.isNotEmpty) {
          await playAtIndex(0, autoPlay: wasPlaying);
        }
      }

      // 确保更新锁屏控制
      await _updateLockscreenControls();
    } catch (e) {
      _log('切换到下一首时出错: $e');
      // 尝试再次开始播放当前曲目或下一曲目，以恢复状态
      try {
        // 记录当前是否正在播放
        bool wasPlaying = _isPlaying;

        if (_currentIndex < _currentPlaylist!.audioFiles.length - 1) {
          await playAtIndex(_currentIndex + 1, autoPlay: wasPlaying);
        } else {
          await playAtIndex(0, autoPlay: wasPlaying); // 循环到第一首
        }
      } catch (retryError) {
        _log('恢复播放失败: $retryError');
      }
    } finally {
      // 延迟重置标志，防止在处理过程中连续触发
      Future.delayed(const Duration(seconds: 2), () {
        _isAdvancingToNextTrack = false;
      });
    }
  }

  // 公开方法，播放下一首音频
  Future<bool> playNext({bool autoAdvance = false}) async {
    // 确保播放列表有效
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      _log('无法播放下一曲: 播放列表为空');
      return false;
    }

    // 防止重复请求切换到下一个音轨
    if (_isAdvancingToNextTrack) {
      _log('已经在切换到下一个音轨，忽略此请求');
      return false;
    }

    _isAdvancingToNextTrack = true;

    try {
      _log('当前索引: $_currentIndex, 总数: ${_currentPlaylist!.audioFiles.length}');

      // 记录切换前的播放状态
      bool wasPlaying = _isPlaying;
      _log('切换前的播放状态: ${wasPlaying ? "播放中" : "已暂停"}，保存此状态用于切换完成后恢复');

      // 保存当前播放位置
      await _saveCurrentAudioPosition();

      // 执行重置操作，清除检查标记
      await _resetPlayback();

      int nextIndex = _currentIndex + 1;

      // 检查是否需要从未完成的文件开始播放
      if (!autoAdvance && nextIndex >= _currentPlaylist!.audioFiles.length) {
        // 查找第一个未完成的音频文件
        int firstUncompletedIndex = -1;
        for (int i = 0; i < _currentPlaylist!.audioFiles.length; i++) {
          if (!_currentPlaylist!.audioFiles[i].isCompleted) {
            firstUncompletedIndex = i;
            break;
          }
        }

        if (firstUncompletedIndex != -1) {
          _log('找到第一个未完成的音频文件，索引: $firstUncompletedIndex');
          nextIndex = firstUncompletedIndex;
        } else {
          _log('所有文件已播放完成，从头开始');
          nextIndex = 0;
        }
      } else if (nextIndex >= _currentPlaylist!.audioFiles.length) {
        // 如果到达列表末尾，循环回开始
        nextIndex = 0;
      }

      _log('将播放下一曲，索引从 $_currentIndex 变更为 $nextIndex');

      // 在清除当前播放项之前，先预先通知锁屏控制器音乐已暂停
      if (_lockscreenService != null) {
        _log('先通知锁屏控制器暂停播放，然后再切换音轨');
        await _lockscreenService!.updatePlaybackState(isPlaying: false);
      }

      // 在切换前先发送播放状态变更事件
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playStateChanged,
        isPlaying: false,
        audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
      ));

      // 短暂延迟确保暂停状态已更新
      await Future.delayed(Duration(milliseconds: 50));

      bool result = false;

      if (_currentIndex != nextIndex) {
        _log('准备播放下一首，自动播放状态设置为: ${wasPlaying ? "播放" : "暂停"}');

        // 使用playAtIndex而不是play，确保完整加载音频文件
        // 根据先前的播放状态决定是否自动播放
        result = await playAtIndex(nextIndex, autoPlay: wasPlaying);

        if (!result) {
          _log('首次播放下一曲失败，尝试重试...');
          // 短暂延迟后重试
          await Future.delayed(Duration(milliseconds: 500));
          result = await playAtIndex(nextIndex, autoPlay: wasPlaying);
        }
      } else {
        _log('下一个索引与当前索引相同，跳过切换');
        result = true;
      }

      // 如果不是自动播放模式，确保更新锁屏控制
      await _updateLockscreenControls();

      return result;
    } catch (e) {
      _log('播放下一曲失败: $e');
      return false;
    } finally {
      _isAdvancingToNextTrack = false;
    }
  }

  // 修改播放上一曲的方法
  Future<bool> playPrevious() async {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      _log('无法播放上一曲: 播放列表为空');
      return false;
    }

    try {
      _log('当前索引: $_currentIndex, 总数: ${_currentPlaylist!.audioFiles.length}');

      // 记录切换前的播放状态
      bool wasPlaying = _isPlaying;
      _log('切换前的播放状态: ${wasPlaying ? "播放中" : "已暂停"}，保存此状态用于切换完成后恢复');

      // 保存当前播放位置
      await _saveCurrentAudioPosition();

      int prevIndex = _currentIndex - 1;
      if (prevIndex < 0) {
        // 循环到列表末尾
        prevIndex = _currentPlaylist!.audioFiles.length - 1;
      }

      _log('将播放上一曲，索引从 $_currentIndex 变更为 $prevIndex');

      // 在清除当前播放项之前，先预先通知锁屏控制器音乐已暂停
      if (_lockscreenService != null) {
        _log('先通知锁屏控制器暂停播放，然后再切换音轨');
        await _lockscreenService!.updatePlaybackState(isPlaying: false);
        // 短暂延迟确保暂停状态已更新
        await Future.delayed(Duration(milliseconds: 50));
      }

      // 在切换前先发送播放状态变更事件
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playStateChanged,
        isPlaying: false,
        audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
      ));

      _log('准备播放上一首，自动播放状态设置为: ${wasPlaying ? "播放" : "暂停"}');

      // 使用playAtIndex播放上一曲，保持原来的播放状态
      bool result = await playAtIndex(prevIndex, autoPlay: wasPlaying);

      // 如果不是自动播放模式，确保更新锁屏控制
      await _updateLockscreenControls();

      return result;
    } catch (e) {
      _log('播放上一曲失败: $e');
      return false;
    }
  }

  // 添加一个方法找到下一个未完成的音频
  int _findNextUncompletedIndex() {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      return -1;
    }

    final audioFiles = _currentPlaylist!.audioFiles;

    // 从当前索引的下一个开始查找
    for (int i = _currentIndex + 1; i < audioFiles.length; i++) {
      if (!audioFiles[i].isCompleted) {
        return i;
      }
    }

    // 如果没找到，从头开始查找，但不超过当前索引
    for (int i = 0; i < _currentIndex; i++) {
      if (!audioFiles[i].isCompleted) {
        return i;
      }
    }

    // 如果所有文件都已完成，返回下一个索引（循环播放）
    return (_currentIndex + 1) % audioFiles.length;
  }

  // 添加一个方法找到前一个未完成的音频
  int _findPreviousUncompletedIndex() {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty) {
      return -1;
    }

    final audioFiles = _currentPlaylist!.audioFiles;

    // 从当前索引的前一个开始查找
    for (int i = _currentIndex - 1; i >= 0; i--) {
      if (!audioFiles[i].isCompleted) {
        return i;
      }
    }

    // 如果没找到，从末尾开始查找，但不小于当前索引
    for (int i = audioFiles.length - 1; i > _currentIndex; i--) {
      if (!audioFiles[i].isCompleted) {
        return i;
      }
    }

    // 如果所有文件都已完成，返回前一个索引（循环播放）
    return _currentIndex > 0 ? _currentIndex - 1 : audioFiles.length - 1;
  }

  Future<void> _updateAudioFilePosition(int positionInSeconds,
      {bool forceUpdate = false}) async {
    if (_currentPlaylist == null ||
        _currentPlaylist!.audioFiles.isEmpty ||
        !_isInitialized) {
      return;
    }

    try {
      _log('>>> 开始更新音频文件位置到数据库 <<<');
      final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
      _log('1. 文件路径: ${audioFile.path}');
      _log('2. 文件名称: ${audioFile.fileName}');
      _log('3. 播放列表ID: ${_currentPlaylist!.id}');
      _log(
          '4. 新位置: ${positionInSeconds}秒 (旧位置: ${audioFile.lastPositionInSeconds}秒)');
      if (forceUpdate) {
        _log('5. 强制更新模式: 忽略节流逻辑和时间差检查');
      }

      // 更新内存中的播放列表数据
      final updatedAudioFile = audioFile.copyWith(
        lastPositionInSeconds: positionInSeconds,
      );

      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      updatedAudioFiles[_currentIndex] = updatedAudioFile;

      _currentPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
        updatedAt: DateTime.now(),
      );
      _log('5. 内存中的播放列表已更新');

      // 不要太频繁地更新数据库，但确保重要的进度变化被保存
      // 减少条件限制，确保更多的进度变化会被保存到数据库
      final now = DateTime.now();
      final timeDiff = now.difference(_lastDbUpdate).inSeconds;
      final posDiff =
          (positionInSeconds - audioFile.lastPositionInSeconds).abs();

      // 时间差超过3秒或位置差超过3秒，才更新数据库
      // 或者设置了forceUpdate标志
      final shouldUpdate = forceUpdate || timeDiff > 3 || posDiff > 3;
      _log(
          '6. 时间差: ${timeDiff}秒, 位置差: ${posDiff}秒, 需要更新数据库: ${shouldUpdate ? "是" : "否"}${forceUpdate ? " (强制更新)" : ""}');

      if (shouldUpdate) {
        _log('7. 执行数据库更新操作');

        // 尝试更新数据库中的播放位置，并传递播放列表ID
        final updateResult = await _databaseHelper.updateAudioFilePosition(
            audioFile.path, positionInSeconds,
            playlistId: _currentPlaylist!.id);

        // 如果没有更新到任何记录（返回0），说明没有该文件的记录，需要插入新记录
        if (updateResult == 0) {
          _log('8. 没有找到现有记录，插入新记录');
          await _databaseHelper.insertPlayHistory(
              audioFile.path, _currentPlaylist!.id, positionInSeconds);
          _log('9. 成功插入新播放位置记录');
        } else {
          _log('8. 已更新现有记录，影响行数: $updateResult');
        }

        // 不再更新整个播放列表到数据库
        _log('9. 跳过播放列表更新，仅保存播放历史记录');

        // 更新最后一次数据库更新时间
        _lastDbUpdate = now;
        _log('10. 数据库更新时间已刷新');
      } else {
        _log('7. 跳过数据库更新');
      }
      _log('>>> 音频文件位置更新完成 <<<');
    } catch (e) {
      _log('更新音频文件位置失败: $e');
      // 出错时不抛出异常，只记录错误
    }
  }

  Future<void> _updateAudioFileCompleted() async {
    if (_currentPlaylist == null ||
        _currentPlaylist!.audioFiles.isEmpty ||
        !_isInitialized ||
        _currentIndex >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    try {
      _log('开始标记音频为已完成状态...');
      final audioFile = _currentPlaylist!.audioFiles[_currentIndex];
      _log('文件名: ${audioFile.fileName}');
      _log('当前完成状态: ${audioFile.isCompleted ? "已完成" : "未完成"}');
      _log('当前位置: ${audioFile.lastPositionInSeconds}秒');
      Logger.warning('开始标记音频为已完成状态,文件名: ${audioFile.fileName}');

      // 使用初始记录的时长或文件模型中的时长
      int finalDurationInSeconds = _initialDurations.containsKey(audioFile.path)
          ? _initialDurations[audioFile.path]!
          : audioFile.durationInSeconds;

      if (finalDurationInSeconds <= 0 && _currentDuration != null) {
        finalDurationInSeconds = _currentDuration!.inSeconds;
      }

      _log('最终使用的总时长: ${finalDurationInSeconds}秒');

      // 更新内存中的播放列表数据
      final updatedAudioFile = audioFile.copyWith(
        isCompleted: true,
        lastPositionInSeconds: finalDurationInSeconds > 0
            ? finalDurationInSeconds
            : audioFile.lastPositionInSeconds,
        durationInSeconds: finalDurationInSeconds > 0
            ? finalDurationInSeconds
            : audioFile.durationInSeconds,
      );

      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      updatedAudioFiles[_currentIndex] = updatedAudioFile;

      _currentPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
        updatedAt: DateTime.now(),
      );

      _log('内存中的播放列表已更新，文件标记为已完成');

      // 更新数据库中的播放列表
      if (_currentPlaylist!.id != null) {
        _log('开始更新数据库...');
        await _databaseHelper.updatePlaylist(_currentPlaylist!);
        _log('数据库更新成功');

        // 为了确保单个文件的播放位置也被更新，直接调用更新位置方法
        if (finalDurationInSeconds > 0) {
          _log('确保文件播放位置更新为总时长...');
          await _databaseHelper.updateAudioFilePosition(
              audioFile.path, finalDurationInSeconds,
              playlistId: _currentPlaylist!.id);
          _log('文件播放位置已更新为总时长');
        }
      }

      // 发送播放列表更新事件通知UI更新
      _log('发送播放列表更新事件通知UI更新');
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playlistUpdated,
        audioFilePath: audioFile.path,
      ));

      // 发送播放状态变更事件
      _log('发送文件状态完成事件通知UI更新');
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playStateChanged,
        isPlaying: false,
        audioFilePath: audioFile.path,
      ));

      _log('音频标记为已完成状态完成');
    } catch (e) {
      _log('更新音频文件完成状态失败: $e');
    }
  }

  // 新增方法：将音频文件标记为未完成状态
  Future<void> markAudioFileAsUnfinished(int index) async {
    if (_currentPlaylist == null ||
        _currentPlaylist!.audioFiles.isEmpty ||
        !_isInitialized ||
        index >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    try {
      _log('开始标记音频为未完成状态...');
      final audioFile = _currentPlaylist!.audioFiles[index];
      _log('文件名: ${audioFile.fileName}');
      _log('当前完成状态: ${audioFile.isCompleted ? "已完成" : "未完成"}');
      _log('当前位置: ${audioFile.lastPositionInSeconds}秒');

      // 如果已经是未完成状态，则不需要任何操作
      if (!audioFile.isCompleted) {
        _log('文件已经是未完成状态，无需修改');
        return;
      }

      // 更新内存中的播放列表数据
      final updatedAudioFile = audioFile.copyWith(
        isCompleted: false,
        lastPositionInSeconds: 0, // 重置播放位置
      );

      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      updatedAudioFiles[index] = updatedAudioFile;

      _currentPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
        updatedAt: DateTime.now(),
      );

      _log('内存中的播放列表已更新，文件标记为未完成');

      // 更新数据库中的播放列表
      if (_currentPlaylist!.id != null) {
        _log('开始更新数据库...');
        await _databaseHelper.updatePlaylist(_currentPlaylist!);
        _log('数据库更新成功');

        // 更新play_history表中的记录，重置播放位置为0
        _log('更新play_history表中的记录...');
        await _databaseHelper.updateAudioFilePosition(audioFile.path, 0,
            playlistId: _currentPlaylist!.id);
        _log('play_history表中的记录已更新，位置重置为0');
      }

      // 发送播放列表更新事件通知UI更新
      _log('发送播放列表更新事件通知UI更新');
      _audioEventBus.fire(AudioEvent(
        eventType: AudioEventType.playlistUpdated,
        audioFilePath: audioFile.path,
      ));

      // 如果当前正在播放的是这个文件，则需要更新播放状态
      if (index == _currentIndex && _isPlaying) {
        _log('当前播放的文件被标记为未完成，更新播放状态');
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.playStateChanged,
          isPlaying: true,
          audioFilePath: audioFile.path,
        ));
      }

      _log('音频标记为未完成状态完成');
    } catch (e) {
      _log('更新音频文件为未完成状态失败: $e');
    }
  }

  Future<void> reorderAudioFiles(int oldIndex, int newIndex) async {
    if (_currentPlaylist == null ||
        oldIndex < 0 ||
        newIndex < 0 ||
        oldIndex >= _currentPlaylist!.audioFiles.length ||
        newIndex >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    try {
      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      final item = updatedAudioFiles.removeAt(oldIndex);
      updatedAudioFiles.insert(newIndex, item);

      // 更新顺序索引
      for (int i = 0; i < updatedAudioFiles.length; i++) {
        updatedAudioFiles[i] = updatedAudioFiles[i].copyWith(orderIndex: i);
      }

      _currentPlaylist = _currentPlaylist!.copyWith(
        audioFiles: updatedAudioFiles,
        updatedAt: DateTime.now(),
      );

      // 更新当前播放索引
      int newCurrentIndex = _currentIndex; // 先保存原来的索引
      if (_currentIndex == oldIndex) {
        newCurrentIndex = newIndex;
      } else if (_currentIndex < oldIndex && _currentIndex >= newIndex) {
        newCurrentIndex = _currentIndex + 1;
      } else if (_currentIndex > oldIndex && _currentIndex <= newIndex) {
        newCurrentIndex = _currentIndex - 1;
      }

      // 使用新方法更新索引
      await _updateCurrentPlayingIndex(newCurrentIndex);

      // 更新数据库中的播放列表
      if (_currentPlaylist!.id != null) {
        await _databaseHelper.updatePlaylist(_currentPlaylist!);
      }
    } catch (e) {
      _log('重新排序音频文件失败: $e');
    }
  }

  Future<void> dispose() async {
    try {
      // 停止计时器
      _stopAdvanceCheckTimer();
      _stopStuckDetectionTimer();

      // 保存当前播放位置
      await _saveCurrentAudioPosition();

      if (_isInitialized) {
        // 不完全释放资源，只是暂停并重置部分状态
        await pause();
      }

      _isPlaying = false;
      _log('AudioService 暂时释放资源完成');
    } catch (e) {
      _log('AudioService dispose 失败: $e');
    }
  }

  // 完全释放资源方法，只在应用退出时调用
  Future<void> disposeCompletely() async {
    _log('完全释放AudioService资源');
    // 停止播放并保存位置
    if (_isPlaying) {
      await pause();
    }
    await saveCurrentAudioPosition();

    // 停止所有定时器
    _stopAdvanceCheckTimer();
    _positionStuckTimer?.cancel();
    _positionStuckTimer = null;

    try {
      // 释放音频播放器资源
      if (_isInitialized && _audioPlayer != null) {
        await _audioPlayer!.release();
        await _audioPlayer!.dispose();
        _audioPlayer = null; // 明确设置为 null，确保下次可以重新创建
        _log('音频播放器资源已释放');
      }
    } catch (e) {
      _log('释放音频播放器资源出错: $e');
      _audioPlayer = null; // 即使出错也确保设为 null
    }

    // 重置状态
    _isInitialized = false;
    _isPlaying = false;
    _log('AudioService已完全释放');
  }

  void _setupStuckDetectionTimer() {
    // 取消之前的定时器（如果有）
    _stopStuckDetectionTimer();

    // 创建新的定时器，每3秒检查一次位置是否卡住
    _positionStuckTimer = Timer.periodic(Duration(seconds: 3), (timer) {
      if (!_isInitialized || !_isPlaying || _isAdvancingToNextTrack) return;

      final currentPos = _currentPosition.inSeconds;

      // 如果位置连续3次未变化，且不是0，且正在播放状态，可能卡住了
      if (currentPos == _lastCheckedPosition && currentPos > 0 && _isPlaying) {
        _stuckCheckCount++;

        // 由于定时器间隔变为3秒，这里的计数逻辑也需要调整
        // 现在2次计数相当于之前的6秒（2*3=6）
        if (_stuckCheckCount >= 2) {
          _log('检测到播放卡住：位置 $currentPos 秒连续 ${_stuckCheckCount * 3} 秒未变化');

          // 获取当前音频总时长
          final currentFile = _currentPlaylist?.audioFiles[_currentIndex];
          final totalDuration = _currentDuration?.inSeconds ??
              (currentFile != null ? currentFile.durationInSeconds : 0);

          // 如果卡在接近结尾或超过一半的位置，尝试切换下一首
          if (totalDuration > 0 && (totalDuration - currentPos) < 1) {
            _log('卡在接近结尾位置，尝试切换到下一首');
            _playNext();
          } else if (_stuckCheckCount >= 3) {
            // 相当于原来的9秒
            // 如果连续卡住较长时间，无论在哪个位置都尝试继续播放
            _log('播放持续卡住，尝试恢复');
            _audioPlayer!.seek(Duration(seconds: currentPos + 1));
          }
        }
      } else {
        // 位置已变化，重置计数
        _stuckCheckCount = 0;
      }

      _lastCheckedPosition = currentPos;
    });

    _log('启动播放卡住检测定时器: 每3秒检查一次');
  }

  void _stopStuckDetectionTimer() {
    _positionStuckTimer?.cancel();
    _positionStuckTimer = null;
  }

  // 添加方法用于持久化存储音频的初始时长
  Future<void> _saveInitialDuration(
      String filePath, int durationInSeconds) async {
    if (durationInSeconds <= 0) return;

    try {
      // 更新数据库中的音频时长
      await _databaseHelper.updateAudioFileDuration(
          filePath, durationInSeconds);
      _log('保存音频文件初始时长到数据库: $filePath, $durationInSeconds 秒');

      // 通知播放列表及所有组件刷新
      if (_currentPlaylist != null && _currentPlaylist!.id != null) {
        // 查找包含此音频文件的所有播放列表，并更新其音频文件的时长
        await _updateAllPlaylistsWithAudioFile(filePath, durationInSeconds);
      }
    } catch (e) {
      _log('保存音频初始时长失败: $e');
    }
  }

  // 添加一个公开的方法，让其他地方也可以调用保存初始时长
  Future<void> saveAudioDuration(String filePath, Duration duration) async {
    if (duration.inSeconds <= 0) return;

    // 确保先记录到内存缓存中
    _initialDurations[filePath] = duration.inSeconds;

    // 然后保存到数据库并同步到所有播放列表
    await _saveInitialDuration(filePath, duration.inSeconds);
  }

  // 更新并存储音频的实际时长
  Future<void> updateAndStoreAudioDuration(
      String filePath, Duration duration) async {
    if (!_isInitialized) {
      await init();
    }

    // 如果时长无效，则不处理
    if (duration.inSeconds <= 0) {
      _log('跳过无效时长: $filePath, ${duration.inSeconds}秒');
      return;
    }

    // 提取文件名，便于日志和调试
    final fileName = filePath.split('/').last.split('\\').last;

    // 防止并发更新同一文件
    if (_updatingFilePaths.contains(filePath)) {
      _log('文件 $fileName 正在更新中，跳过重复更新');
      return;
    }

    // 标记为正在更新
    _updatingFilePaths.add(filePath);

    try {
      _log('开始更新音频时长: $fileName, ${duration.inSeconds}秒');

      // 1. 更新内存记录
      _initialDurations[filePath] = duration.inSeconds;
      _log('内存记录已更新: $fileName => ${duration.inSeconds}秒');

      // 2. 获取数据库连接
      final db = await _databaseHelper.database;
      _log('数据库连接已获取');

      // 3. 检查记录是否存在
      final List<Map<String, dynamic>> existingRecords = await db.query(
        'audio_files',
        where: 'path = ?',
        whereArgs: [filePath],
      );

      if (existingRecords.isNotEmpty) {
        _log('记录存在: $fileName');
        final Map<String, dynamic> existingRecord = existingRecords.first;
        final int oldDuration = existingRecord['duration_in_seconds'] ?? 0;
        final int initialDuration =
            existingRecord['initial_duration_in_seconds'] ?? 0;

        _log('现有记录时长: $oldDuration 秒, 初始时长: $initialDuration 秒');

        // 时长差异在2秒内认为是相同的，避免微小差异导致过多更新
        if ((oldDuration - duration.inSeconds).abs() <= 2) {
          _log('时长相近，无需更新: $oldDuration 秒 vs ${duration.inSeconds}秒');
        } else {
          // 需要更新时长
          _log(
              '更新数据库记录: $fileName, 旧时长: $oldDuration 秒, 新时长: ${duration.inSeconds}秒');

          // 如果初始时长为0，同时更新初始时长
          Map<String, dynamic> updateValues = {
            'duration_in_seconds': duration.inSeconds,
            'last_updated': DateTime.now().millisecondsSinceEpoch
          };

          if (initialDuration == 0) {
            updateValues['initial_duration_in_seconds'] = duration.inSeconds;
            _log('同时更新初始时长: ${duration.inSeconds}秒');
          }

          await db.update(
            'audio_files',
            updateValues,
            where: 'path = ?',
            whereArgs: [filePath],
          );

          _log('数据库记录已更新: $fileName => ${duration.inSeconds}秒');

          // 如果时长变化，广播事件通知其他组件
          if (oldDuration != duration.inSeconds) {
            // 发送事件通知
            _log('发送时长变更事件: $fileName, ${duration.inSeconds}秒');
            _audioEventBus.fire(AudioEvent(
              eventType: AudioEventType.durationChanged,
              audioFilePath: filePath,
              duration: duration,
            ));
          }
        }
      } else {
        _log('记录不存在，添加新记录: $fileName');

        // 创建新记录
        await db.insert(
          'audio_files',
          {
            'path': filePath,
            'duration_in_seconds': duration.inSeconds,
            'initial_duration_in_seconds': duration.inSeconds,
            'last_updated': DateTime.now().millisecondsSinceEpoch
          },
        );

        _log('已添加新记录: $fileName, 时长: ${duration.inSeconds}秒');

        // 广播时长变更事件
        _log('发送时长变更事件: $fileName, ${duration.inSeconds}秒');
        _audioEventBus.fire(AudioEvent(
          eventType: AudioEventType.durationChanged,
          audioFilePath: filePath,
          duration: duration,
        ));
      }

      // 4. 更新包含此音频文件的所有播放列表
      _log('开始更新所有包含该文件的播放列表');
      await _databaseHelper.updateAllPlaylistsWithAudioFile(
          filePath, duration.inSeconds);
      _log('所有播放列表已更新');
    } catch (e) {
      _log('更新音频时长出错: $e');
    } finally {
      // 无论成功失败，都移除正在更新标记
      _updatingFilePaths.remove(filePath);
    }
  }

  // 在所有包含该音频文件的播放列表中更新时长
  Future<void> _updateAllPlaylistsWithAudioFile(
      String filePath, int durationInSeconds) async {
    try {
      // 获取所有播放列表
      final playlists = await _databaseHelper.getAllPlaylists();

      for (var playlist in playlists) {
        bool needsUpdate = false;
        final updatedAudioFiles =
            List<AudioFileModel>.from(playlist.audioFiles);

        for (int i = 0; i < updatedAudioFiles.length; i++) {
          if (updatedAudioFiles[i].path == filePath) {
            // 更新音频文件时长
            updatedAudioFiles[i] = updatedAudioFiles[i].copyWith(
              durationInSeconds: durationInSeconds,
            );
            needsUpdate = true;
          }
        }

        if (needsUpdate) {
          // 创建更新后的播放列表
          final updatedPlaylist = playlist.copyWith(
            audioFiles: updatedAudioFiles,
            updatedAt: DateTime.now(),
          );

          // 更新数据库
          await _databaseHelper.updatePlaylist(updatedPlaylist);
          _log('已在播放列表 ${playlist.name} 中更新音频文件时长');

          // 如果是当前正在播放的播放列表，也更新内存中的列表
          if (_currentPlaylist != null && _currentPlaylist!.id == playlist.id) {
            _currentPlaylist = updatedPlaylist;
          }
        }
      }
    } catch (e) {
      _log('更新包含音频文件的播放列表时出错: $e');
    }
  }

  // 添加方法用于从数据库加载音频的初始时长
  Future<void> _loadInitialDurationsFromDatabase() async {
    if (_currentPlaylist == null || _currentPlaylist!.audioFiles.isEmpty)
      return;

    try {
      for (var audioFile in _currentPlaylist!.audioFiles) {
        if (!_initialDurations.containsKey(audioFile.path)) {
          // 先尝试从文件模型获取
          if (audioFile.durationInSeconds > 0) {
            _initialDurations[audioFile.path] = audioFile.durationInSeconds;
            // 模型有时长，但没有在数据库中保存，确保保存一次
            _saveInitialDuration(audioFile.path, audioFile.durationInSeconds);
          } else {
            // 如果模型没有时长，尝试从数据库获取
            int duration =
                await _databaseHelper.getAudioFileDuration(audioFile.path);
            if (duration > 0) {
              _initialDurations[audioFile.path] = duration;
              _log('从数据库加载音频时长: ${audioFile.fileName}, $duration 秒');

              // 如果数据库中的时长与模型不同，则更新模型的时长
              if (audioFile.durationInSeconds != duration) {
                _updateAudioFileInCurrentPlaylist(audioFile.path, duration);
              }
            }
          }
        }
      }
    } catch (e) {
      _log('从数据库加载初始时长失败: $e');
    }
  }

  // 在当前播放列表中更新特定音频文件的时长
  void _updateAudioFileInCurrentPlaylist(
      String filePath, int durationInSeconds) {
    if (_currentPlaylist == null) return;

    try {
      final updatedAudioFiles =
          List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
      bool updated = false;

      for (int i = 0; i < updatedAudioFiles.length; i++) {
        if (updatedAudioFiles[i].path == filePath) {
          updatedAudioFiles[i] = updatedAudioFiles[i].copyWith(
            durationInSeconds: durationInSeconds,
          );
          updated = true;
        }
      }

      if (updated) {
        _currentPlaylist = _currentPlaylist!.copyWith(
          audioFiles: updatedAudioFiles,
          updatedAt: DateTime.now(),
        );

        // 如果有ID，持久化到数据库
        if (_currentPlaylist!.id != null) {
          _databaseHelper.updatePlaylist(_currentPlaylist!);
        }
      }
    } catch (e) {
      _log('更新当前播放列表中音频文件时长失败: $e');
    }
  }

  // 添加方法更新当前音频文件的时长
  void _updateCurrentAudioFileDuration(int durationInSeconds) {
    if (_currentPlaylist == null ||
        _currentIndex < 0 ||
        _currentIndex >= _currentPlaylist!.audioFiles.length) {
      return;
    }

    try {
      final currentFile = _currentPlaylist!.audioFiles[_currentIndex];

      // 只有当新时长有效且与当前不同时才更新
      if (durationInSeconds > 0 &&
          currentFile.durationInSeconds != durationInSeconds) {
        final updatedAudioFiles =
            List<AudioFileModel>.from(_currentPlaylist!.audioFiles);
        updatedAudioFiles[_currentIndex] = currentFile.copyWith(
          durationInSeconds: durationInSeconds,
        );

        _currentPlaylist = _currentPlaylist!.copyWith(
          audioFiles: updatedAudioFiles,
          updatedAt: DateTime.now(),
        );

        // 确保时长也保存到数据库audio_files表
        _saveInitialDuration(currentFile.path, durationInSeconds);
      }
    } catch (e) {
      _log('更新当前音频文件时长失败: $e');
    }
  }

  // 重置当前播放曲目时，需要清除该文件的检查标记
  Future<void> _resetPlayback() async {
    if (_currentPlaylist != null &&
        _currentIndex >= 0 &&
        _currentIndex < _currentPlaylist!.audioFiles.length) {
      final currentPath = _currentPlaylist!.audioFiles[_currentIndex].path;
      // 移除当前文件的检查标记，允许在下次播放时重新检查
      _durationCheckedFiles.remove(currentPath);
    }

    // 重置标志
    _isAdvancingToNextTrack = false;
  }

  // 特殊方法：仅用于计算音频文件时长
  Future<Duration> calculateAudioFileDuration(String filePath) async {
    if (!_isInitialized) {
      await init();
    }

    // 如果该文件已经在计算中，跳过重复计算
    if (_calculatingDurations.contains(filePath)) {
      _log('文件 $filePath 正在计算中，跳过重复计算');

      // 返回当前数据库中的时长，如果有的话
      final dbDuration = await _databaseHelper.getAudioFileDuration(filePath);
      if (dbDuration > 0) {
        return Duration(seconds: dbDuration);
      }

      // 如果数据库中没有，返回零时长，让调用者决定下一步
      return Duration.zero;
    }

    // 标记该文件正在计算
    _calculatingDurations.add(filePath);
    _log('开始计算文件时长: $filePath');

    // 创建临时播放器实例
    final tempPlayer = audio_players.AudioPlayer();

    try {
      // 使用 just_audio 方式获取时长
      // 设置音频源但不播放，just_audio 会自动解析元数据
      _log('尝试设置音频源并获取时长...');

      // 确保文件存在
      final file = File(filePath);
      if (!file.existsSync()) {
        _log('错误: 文件不存在: $filePath');
        return Duration.zero;
      }

      final fileName = file.path.split('/').last.split('\\').last;

      // 设置音量为0，确保即使意外播放也不会有声音
      await tempPlayer.setVolume(0);

      // 设置音频源 - 这一步会加载元数据但不会播放
      final source = audio_players.DeviceFileSource(filePath);
      await tempPlayer.setSource(source);

      // 设置超时
      final Duration timeout = Duration(seconds: 5);

      // 使用 onDurationChanged 获取时长
      final completer = Completer<Duration>();
      Duration? resultDuration;

      // 监听时长变化
      var durationSubscription =
          tempPlayer.onDurationChanged.listen((duration) {
        _log('获取到时长: ${duration.inSeconds}秒');
        if (!completer.isCompleted && duration.inMilliseconds > 0) {
          resultDuration = duration;
          completer.complete(duration);
        }
      });

      // 设置超时处理
      Future<Duration> timeoutFuture = Future.delayed(timeout, () {
        if (!completer.isCompleted) {
          _log('获取时长超时，使用可用的最新值');
          return resultDuration ?? Duration.zero;
        }
        return resultDuration ?? Duration.zero;
      });

      // 等待获取到时长或超时
      final duration = await Future.any([completer.future, timeoutFuture]);

      // 取消订阅
      await durationSubscription.cancel();

      // 一旦获取到时长，立即保存到数据库并更新所有相关组件
      if (duration.inSeconds > 0) {
        _log('计算完成: $fileName, 精确时长: ${duration.inSeconds}秒');
        _log('开始保存时长到数据库...');

        final saveStartTime = DateTime.now();
        // 保存时长到数据库
        await updateAndStoreAudioDuration(filePath, duration);

        final saveEndTime = DateTime.now();
        final saveTime = saveEndTime.difference(saveStartTime).inMilliseconds;
        _log('时长已保存到数据库，耗时: ${saveTime}ms');
      }

      return duration;
    } catch (e) {
      _log('计算音频时长时出错: $e');
      return Duration.zero;
    } finally {
      // 确保释放临时播放器资源
      try {
        await tempPlayer.dispose();
        _log('临时播放器资源已释放');
      } catch (e) {
        _log('释放临时播放器资源出错: $e');
      }

      // 从计算集合中移除，无论成功失败
      _calculatingDurations.remove(filePath);
    }
  }

  // 提供对音频事件总线的访问
  AudioEventBus get audioEventBus => _audioEventBus;

  // 获取指定音频文件在所有播放列表中的播放位置
  Future<Map<String, int>> getAudioFilePositionsInAllPlaylists(
      String audioFilePath) async {
    try {
      if (!_isInitialized) {
        await init();
      }

      // 获取文件在所有播放列表中的位置
      final Map<int, int> playlistPositions =
          await _databaseHelper.getAllPlaylistPositionsForAudio(audioFilePath);

      // 将播放列表ID转换为播放列表名称
      final Map<String, int> namedPositions = {};

      for (var entry in playlistPositions.entries) {
        final playlistId = entry.key;
        final position = entry.value;

        // 获取播放列表名称
        final name = await _databaseHelper.getPlaylistName(playlistId);
        if (name != null) {
          // 格式化播放时间
          namedPositions[name] = position;
        }
      }

      return namedPositions;
    } catch (e) {
      _log('获取音频文件在所有播放列表中的位置失败: $e');
      return {};
    }
  }

  // 获取格式化的播放位置信息
  Future<String> getFormattedAudioPositionsInfo(String audioFilePath) async {
    final positions = await getAudioFilePositionsInAllPlaylists(audioFilePath);

    if (positions.isEmpty) {
      return "该音频文件在任何播放列表中都没有播放记录";
    }

    final StringBuffer buffer = StringBuffer();
    buffer.writeln("音频文件在各播放列表中的播放位置:");

    positions.forEach((playlistName, positionInSeconds) {
      final formattedTime =
          _formatDuration(Duration(seconds: positionInSeconds));
      buffer.writeln("- $playlistName: $formattedTime");
    });

    return buffer.toString();
  }

  // 格式化时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    return duration.inHours > 0
        ? '$hours:$minutes:$seconds'
        : '$minutes:$seconds';
  }

  // 添加一个方法，用于在 _currentIndex 变化时同时更新 PlaylistModel 的 currentPlayingIndex 字段
  Future<void> _updateCurrentPlayingIndex(int index) async {
    if (_currentPlaylist == null || !_isInitialized) {
      return;
    }

    // 更新内存中的索引
    _currentIndex = index;

    // 更新播放列表模型中的currentPlayingIndex
    _currentPlaylist = _currentPlaylist!.copyWith(
      currentPlayingIndex: index,
      updatedAt: DateTime.now(),
    );

    // 如果有ID，更新数据库
    if (_currentPlaylist!.id != null) {
      try {
        // 只更新currentPlayingIndex字段，避免完整的播放列表更新
        final db = await _databaseHelper.database;
        await db.update(
          'playlists',
          {
            'currentPlayingIndex': index,
            'updatedAt': DateTime.now().millisecondsSinceEpoch
          },
          where: 'id = ?',
          whereArgs: [_currentPlaylist!.id],
        );
        _log(
            '已更新播放列表currentPlayingIndex到数据库: 索引=$index, 播放列表ID=${_currentPlaylist!.id}');
      } catch (e) {
        _log('更新currentPlayingIndex到数据库失败: $e');
      }
    }
  }

  // 保留这些函数，但函数体为空
  void _startAdvanceCheckTimer() {
    // 函数体为空，不再执行任何操作
  }

  void _stopAdvanceCheckTimer() {
    _advanceCheckTimer?.cancel();
    _advanceCheckTimer = null;
  }

  // 从播放列表加载播放速度设置
  void _loadPlaybackSpeedFromPlaylist() {
    if (_currentPlaylist == null) {
      _log('无法加载播放速度：当前播放列表为空');
      return;
    }

    // 从播放列表获取速度
    final speed = _currentPlaylist!.playbackSpeed;
    _log('从播放列表加载播放速度: $speed');

    // 更新内存中的播放速度
    final oldSpeed = _playbackSpeed;
    _playbackSpeed = speed;

    // 如果播放器已初始化，则设置播放速度
    if (_isInitialized && _audioPlayer != null) {
      try {
        _log('正在设置播放器的播放速度: $speed');
        _audioPlayer!.setPlaybackRate(speed);
        _log('播放器播放速度已设置为: $speed');

        // 如果速度发生变化，广播速度变更事件
        if (oldSpeed != speed) {
          _log('广播播放速度变更事件: $speed');
          _audioEventBus.fire(AudioEvent(
            eventType: AudioEventType.playbackSpeedChanged,
            speed: speed,
          ));
        }
      } catch (e) {
        _log('设置播放器播放速度失败: $e');
      }
    } else {
      _log('播放器未初始化，仅更新内存中的速度');
    }
  }

  // 更新锁屏控制
  Future<void> _updateLockscreenControls() async {
    // 添加节流逻辑，避免频繁更新
    final now = DateTime.now();
    final timeSinceLastUpdate =
        now.difference(_lastLockscreenUpdateTime).inMilliseconds;

    // 增加更新间隔，从300毫秒提高到1500毫秒
    if (timeSinceLastUpdate < 50) {
      return;
    }

    _lastLockscreenUpdateTime = now;

    try {
      if (_lockscreenService != null &&
          _currentPlaylist != null &&
          _currentIndex >= 0 &&
          _currentIndex < _currentPlaylist!.audioFiles.length) {
        // 获取当前音频文件信息
        final audioFile = _currentPlaylist!.audioFiles[_currentIndex];

        _log(
            '更新锁屏控制: ${audioFile.fileName}, 播放状态: ${_isPlaying ? "播放中" : "已暂停"}');

        // 更新当前媒体信息
        await _lockscreenService!
            .updateCurrentMediaInfo(_currentPlaylist!, _currentIndex);

        // 更新播放状态
        await _lockscreenService!.updatePlaybackState(isPlaying: _isPlaying);
      }
    } catch (e) {
      _log('更新锁屏控制失败: $e');
    }
  }

  // 增强的音频事件处理方法
  void _handleAudioEvent(AudioEvent event) {
    if (!_isInitialized) return;

    // 当发生以下事件时，更新锁屏控制
    if (event.eventType == AudioEventType.playStateChanged ||
        event.eventType == AudioEventType.playlistUpdated ||
        event.eventType == AudioEventType.durationChanged ||
        event.eventType == AudioEventType.playbackSpeedChanged) {
      // 日志记录，指明是通过哪种事件触发的锁屏更新
      _log('通过事件 ${event.eventType} 触发锁屏控制更新');

      // 添加短暂延迟，确保状态已完全更新
      Future.delayed(Duration(milliseconds: 50), () {
        _updateLockscreenControls();
      });
    }
  }

  void _log(String message) {
    if (kDebugMode) {
      Logger.info(message);
    }
  }

  // 处理耳机断开事件
  Future<void> _handleHeadphoneDisconnect() async {
    try {
      if (!_isInitialized || _audioPlayer == null) {
        _log('耳机断开时播放器未初始化或为null，无需处理');
        return;
      }

      if (!_isPlaying) {
        _log('耳机断开时播放器未在播放状态，无需处理');
        return;
      }

      _log('处理耳机断开事件，自动暂停播放');
      try {
        // 保存当前播放位置
        await _saveCurrentAudioPosition();

        // 暂停播放
        await pause();

        // 发送播放状态变化事件
        if (_currentPlaylist != null &&
            _currentIndex >= 0 &&
            _currentIndex < _currentPlaylist!.audioFiles.length) {
          _audioEventBus.fire(AudioEvent(
            eventType: AudioEventType.playStateChanged,
            isPlaying: false,
            audioFilePath: _currentPlaylist!.audioFiles[_currentIndex].path,
          ));
        }
      } catch (e) {
        _log('处理耳机断开事件内部操作失败: $e');
      }
    } catch (e) {
      _log('处理耳机断开事件失败: $e');
    }
  }
}
