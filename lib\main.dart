import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:flutter/foundation.dart'; // 导入foundation.dart获取kDebugMode
import 'package:permission_handler/permission_handler.dart';
import 'screens/home_screen.dart';
import 'utils/app_theme.dart';
import 'services/audio_service.dart';
import 'services/file_service.dart';
import 'database/database_helper.dart';

// 添加日志函数，在非调试模式下不输出
void _log(String message) {
  // 暂时禁用输出，仅在database_helper中保留
  // if (kDebugMode) {
  //   print(message);
  // }
}

// 全局变量保存后台播放是否初始化成功 - 固定设置为 false，不再使用后台播放
bool isBackgroundAudioInitialized = false;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置屏幕方向为竖屏
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // 设置状态栏颜色
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
    ),
  );

  // 初始化数据库并启用SQL日志
  try {
    final dbHelper = DatabaseHelper();
    dbHelper.setSqlLogging(true); // 启用SQL日志
    _log('SQL日志记录已启用，所有SQL查询将在控制台显示');
    // 预先初始化数据库连接
    await dbHelper.database;
    _log('数据库连接已初始化');
  } catch (e) {
    _log('初始化数据库失败: $e');
  }

  // 初始化FileService并强制请求所有必要权限
  final fileService = FileService();

  // 强制请求所有权限，重点处理华为等设备
  try {
    _log('应用启动，开始请求所有必要权限');
    final hasAllPermissions = await fileService.requestAllPermissions();
    _log('请求所有权限结果: ${hasAllPermissions ? "成功" : "部分成功或失败"}');

    // 如果权限未全部获取成功，尝试再次获取关键权限
    if (!hasAllPermissions && Platform.isAndroid) {
      await Future.delayed(const Duration(milliseconds: 500));
      await fileService.checkStoragePermission();
    }
  } catch (e) {
    _log('权限请求过程中出错: $e');
  }

  // 获取通知权限，显式请求以确保可以使用通知功能
  try {
    final notificationPermission = await Permission.notification.status;
    if (!notificationPermission.isGranted) {
      _log('通知权限未授予，正在请求...');
      final permissionResult = await Permission.notification.request();
      if (permissionResult.isGranted) {
        _log('成功获取通知权限');
      } else {
        _log('用户拒绝通知权限，这将影响部分功能');
      }
    }
  } catch (e) {
    _log('请求通知权限时出错: $e');
  }

  // 给系统一些时间先准备资源
  await Future.delayed(const Duration(milliseconds: 100));

  // 确保后台播放状态始终为 false，完全禁用后台播放功能
  isBackgroundAudioInitialized = false;
  _log('后台播放功能已禁用，应用将使用基本播放模式');

  // 初始化服务，使用基本模式
  final audioService = AudioService();
  try {
    await audioService.init();
    _log('音频服务初始化成功，使用基本播放模式');
  } catch (e) {
    _log('音频服务初始化失败: $e');

    // 如果初始化失败，尝试重新初始化
    try {
      isBackgroundAudioInitialized = false; // 确保使用基本模式
      await audioService.init();
      _log('音频服务以基本模式初始化成功');
    } catch (e2) {
      _log('音频服务以基本模式初始化也失败: $e2');
    }
  }

  // 注册退出时的清理函数
  WidgetsBinding.instance.addObserver(AppLifecycleObserver(audioService));

  // 打印平台相关信息，帮助调试
  if (Platform.isAndroid) {
    _log('设备操作系统: Android');
    _log('操作系统版本: ${Platform.operatingSystemVersion}');
    try {
      final permission = fileService.getPermissionFriendlyName();
      _log('应用需要的权限类型: $permission');
    } catch (e) {
      _log('获取权限类型失败: $e');
    }
  }

  runApp(const MyApp());
}

// 添加生命周期观察者，用于在应用退出时释放资源
class AppLifecycleObserver extends WidgetsBindingObserver {
  final AudioService audioService;

  AppLifecycleObserver(this.audioService);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.detached) {
      // 应用被终止，释放资源
      _log('应用终止，完全释放音频资源');
      audioService.disposeCompletely();
    } else if (state == AppLifecycleState.paused) {
      // 应用进入后台，确保播放位置被保存，但不暂停播放
      audioService.saveCurrentAudioPosition();
      _log('应用进入后台，正常保存播放位置，继续播放');
    } else if (state == AppLifecycleState.resumed) {
      // 应用从后台恢复，检查播放器状态和权限
      _log('应用恢复前台，检查播放器状态和权限');

      // 检查播放器状态
      if (!audioService.isInitialized) {
        _log('应用恢复前台时检测到音频服务未初始化，尝试重新初始化');
        audioService.init();
      }

      // 检查权限状态，可能系统设置被修改
      final fileService = FileService();
      fileService.checkStoragePermission();
    }
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '本地音频播放器',
      theme: AppTheme.getLightTheme(),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
