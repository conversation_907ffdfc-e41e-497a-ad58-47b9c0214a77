import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/playlist_model.dart';
import '../widgets/playlist_card.dart';
import '../database/database_helper.dart';
import '../services/audio_service.dart';
import '../services/file_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'playlist_edit_screen.dart';
import 'playlist_player_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final AudioService _audioService = AudioService();
  final FileService _fileService = FileService();
  List<PlaylistModel> _playlists = [];
  bool _isLoading = true;
  bool _hasStoragePermission = false;
  bool _isRequestingPermission = false;

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  void _log(String message) {
    // 日志函数
  }

  Future<void> _initServices() async {
    // 先初始化数据库和音频服务，不等待权限
    await Future.wait([
      _initAudioService(),
      _loadPlaylists(),
    ]);

    // 然后请求权限
    await _requestStoragePermission();
  }

  Future<void> _requestStoragePermission() async {
    if (_isRequestingPermission) return; // 防止重复请求

    setState(() {
      _isRequestingPermission = true;
    });

    try {
      // 使用新的全面权限请求方法
      final hasPermission = await _fileService.requestAllPermissions();

      if (mounted) {
        setState(() {
          _hasStoragePermission = hasPermission;
          _isRequestingPermission = false;
        });

        // 如果权限获取成功，刷新播放列表
        if (hasPermission) {
          _loadPlaylists();
        } else {
          // 如果权限请求失败且应用已挂载，显示提示
          // 获取权限的友好名称
          final permissionName = _fileService.getPermissionFriendlyName();

          // 使用更友好的UI提示，不要马上弹SnackBar，改为后期无法使用时再提示
          if (hasPermission == false) {
            // 华为设备特殊处理，提醒用户可能需要重启应用
            if (await _isHuaweiDevice()) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('华为设备需要$permissionName才能访问本地音频文件，授权后可能需要重启应用'),
                  duration: const Duration(seconds: 8),
                  action: SnackBarAction(
                    label: '设置',
                    onPressed: () => openAppSettings(),
                  ),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('需要$permissionName才能访问本地音频文件'),
                  duration: const Duration(seconds: 5),
                  action: SnackBarAction(
                    label: '设置',
                    onPressed: () => openAppSettings(),
                  ),
                ),
              );
            }
          }
        }
      }
    } catch (e) {
      _log('请求权限失败: $e');
      setState(() {
        _isRequestingPermission = false;
      });
    }
  }

  // 检测是否为华为设备的辅助方法
  Future<bool> _isHuaweiDevice() async {
    try {
      return await _fileService.isHuaweiDevice();
    } catch (e) {
      return false;
    }
  }

  // 调试权限状态的方法
  Future<void> _debugPermissionStatus() async {
    // 打印详细的权限状态信息
    await _fileService.debugPrintPermissionStatus();

    // 测试checkStoragePermission方法
    final result = await _fileService.checkStoragePermission();
    if (kDebugMode) {
      print('[HomeScreen] checkStoragePermission 结果: $result');
    }

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('权限检查结果: $result\n详细信息请查看控制台日志'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _initAudioService() async {
    await _audioService.init();
  }

  Future<void> _loadPlaylists() async {
    setState(() => _isLoading = true);

    try {
      final playlists = await _databaseHelper.getAllPlaylists();
      if (mounted) {
        setState(() {
          _playlists = playlists;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载播放列表失败: $e')),
        );
        setState(() => _isLoading = false);
      }
    }
  }

  void _createNewPlaylist() async {
    // 检查存储权限
    if (!_hasStoragePermission) {
      final granted = await _fileService.requestAllPermissions();
      setState(() {
        _hasStoragePermission = granted;
      });

      if (!granted) {
        if (mounted) {
          // 获取权限的友好名称
          final permissionName = _fileService.getPermissionFriendlyName();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('需要$permissionName才能创建播放列表'),
              duration: const Duration(seconds: 5),
              action: SnackBarAction(
                label: '设置',
                onPressed: () => openAppSettings(),
              ),
            ),
          );
        }
        return;
      }
    }

    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PlaylistEditScreen(),
      ),
    );

    // 刷新播放列表
    _loadPlaylists();
  }

  void _editPlaylist(PlaylistModel playlist) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaylistEditScreen(playlist: playlist),
      ),
    );

    // 刷新播放列表
    _loadPlaylists();
  }

  Future<void> _deletePlaylist(PlaylistModel playlist) async {
    // 显示确认对话框
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除播放列表'),
        content: Text('确定要删除播放列表 "${playlist.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirm == true && playlist.id != null) {
      try {
        await _databaseHelper.deletePlaylist(playlist.id!);
        _loadPlaylists();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已删除播放列表 "${playlist.name}"')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除播放列表失败: $e')),
        );
      }
    }
  }

  void _playPlaylist(PlaylistModel playlist) async {
    if (playlist.audioFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('播放列表为空，请先添加音频文件')),
      );
      return;
    }

    // 在打开播放界面前，从数据库获取最新的播放列表数据
    PlaylistModel updatedPlaylist = playlist;
    try {
      if (playlist.id != null) {
        final latestPlaylist = await _databaseHelper.getPlaylist(playlist.id!);
        if (latestPlaylist != null) {
          updatedPlaylist = latestPlaylist;
        }
      }
    } catch (e) {
      _log('获取最新播放列表数据失败: $e');
    }

    if (updatedPlaylist.audioFiles.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('播放列表为空，请先添加音频文件')),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaylistPlayerScreen(playlist: updatedPlaylist),
      ),
    ).then((_) {
      // 当用户从播放界面返回时，刷新播放列表数据
      _loadPlaylists();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('本地音频播放器'),
        actions: [
          // 只在调试模式下显示调试按钮
          if (kDebugMode)
            IconButton(
              icon: const Icon(Icons.bug_report),
              onPressed: _debugPermissionStatus,
              tooltip: '调试权限状态',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPlaylists,
            tooltip: '刷新',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _playlists.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.queue_music,
                        size: 80,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '暂无播放列表',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        '点击下方按钮创建新的播放列表',
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _createNewPlaylist,
                        child: const Text('创建播放列表'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.only(bottom: 80),
                  itemCount: _playlists.length,
                  itemBuilder: (context, index) {
                    final playlist = _playlists[index];
                    return PlaylistCard(
                      playlist: playlist,
                      onTap: () => _playPlaylist(playlist),
                      onEdit: () => _editPlaylist(playlist),
                      onDelete: () => _deletePlaylist(playlist),
                    );
                  },
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewPlaylist,
        icon: const Icon(Icons.add),
        label: const Text('新建播放列表'),
      ),
    );
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
