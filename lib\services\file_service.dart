import 'dart:io';
import 'dart:async';
import 'package:file_picker/file_picker.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:path/path.dart' as path;
import 'package:permission_handler/permission_handler.dart';
import '../models/playlist_model.dart';
import '../database/database_helper.dart';
import '../services/audio_service.dart';
import 'package:flutter/foundation.dart'; // 导入kDebugMode

class FileService {
  static final FileService _instance = FileService._internal();
  factory FileService() => _instance;

  final DatabaseHelper _databaseHelper = DatabaseHelper();

  // 添加权限请求状态标记
  bool _permissionRequested = false;

  // 添加上次请求权限的时间记录，用于处理系统升级情况
  DateTime? _lastPermissionRequestTime;

  // 添加设备制造商缓存
  String? _cachedManufacturer;

  FileService._internal();

  // 支持的音频格式
  final List<String> _supportedAudioFormats = [
    'mp3',
    'wav',
    'aac',
    'ogg',
    'm4a',
    'flac',
    'wma'
  ];

  // 添加日志函数，在非调试模式下不输出
  void _log(String message) {
    if (kDebugMode) {
      print('[FileService] $message');
    }
  }

  // 获取权限的友好名称，用于显示给用户
  String getPermissionFriendlyName() {
    if (Platform.isAndroid) {
      return '存储权限';
    } else if (Platform.isIOS) {
      return '照片访问权限';
    } else {
      return '相关权限';
    }
  }

  // 同步检查 Android 版本，用于 UI 显示
  bool _isAndroid13OrHigherSync() {
    if (Platform.isAndroid) {
      try {
        // 这只是一个粗略的检查，用于 UI 提示
        final sdkInt =
            int.parse(Platform.operatingSystemVersion.split('.').first);
        return sdkInt >= 13;
      } catch (e) {
        return false;
      }
    }
    return false;
  }

  // 获取设备制造商
  Future<String> _getDeviceManufacturer() async {
    // 如果已经缓存了制造商信息，直接返回
    if (_cachedManufacturer != null) {
      return _cachedManufacturer!;
    }

    if (Platform.isAndroid) {
      try {
        final deviceInfo = DeviceInfoPlugin();
        final androidInfo = await deviceInfo.androidInfo;
        _cachedManufacturer = androidInfo.manufacturer.toLowerCase();
        return _cachedManufacturer!;
      } catch (e) {
        _log('获取设备制造商信息失败: $e');
        return '';
      }
    }
    return '';
  }

  // 检查是否是华为设备
  Future<bool> _isHuaweiDevice() async {
    final manufacturer = await _getDeviceManufacturer();
    return manufacturer.contains('huawei') || manufacturer.contains('honor');
  }

  // 强制请求所有可能需要的权限，用于应用启动时或系统升级后
  Future<bool> requestAllPermissions() async {
    bool hasPermission = false;

    // 记录当前请求时间
    _lastPermissionRequestTime = DateTime.now();
    _permissionRequested = true;

    try {
      _log('开始请求所有必要权限');

      // 对于华为设备，优先请求管理外部存储权限
      if (await _isHuaweiDevice()) {
        _log('检测到华为设备，请求特殊权限');

        // 先请求MANAGE_EXTERNAL_STORAGE权限
        final manageStatus = await Permission.manageExternalStorage.request();
        if (manageStatus.isGranted) {
          _log('已获得管理外部存储权限');
          hasPermission = true;
        } else {
          _log('管理外部存储权限请求失败，尝试其他权限');

          // 再请求标准存储权限
          final storageStatus = await Permission.storage.request();
          hasPermission = storageStatus.isGranted;

          // 对于Android 13及以上设备，请求媒体权限
          if (await _isAndroid13OrHigher()) {
            final mediaAudioStatus = await Permission.audio.request();
            hasPermission = hasPermission || mediaAudioStatus.isGranted;
          }
        }
      } else {
        // 非华为设备，请求标准权限
        if (await _isAndroid13OrHigher()) {
          // Android 13+设备请求媒体权限
          final mediaAudioStatus = await Permission.audio.request();
          hasPermission = mediaAudioStatus.isGranted;
        } else {
          // 旧版Android请求存储权限
          final storageStatus = await Permission.storage.request();
          hasPermission = storageStatus.isGranted;
        }
      }

      // 请求通知权限
      await Permission.notification.request();

      _log('所有权限请求完成，结果: ${hasPermission ? "成功" : "失败"}');
      return hasPermission;
    } catch (e) {
      _log('请求权限过程中出错: $e');
      return false;
    }
  }

  // 检查是否需要重新请求权限
  // 例如：应用启动后经过了较长时间，或者检测到系统可能升级了
  Future<bool> _shouldReRequestPermissions() async {
    _log('检查是否需要重新请求权限');

    // 如果之前未请求过权限，应该请求
    if (!_permissionRequested || _lastPermissionRequestTime == null) {
      _log('之前未请求过权限，需要请求');
      return true;
    }

    // 如果距离上次请求权限超过1天，重新请求
    final now = DateTime.now();
    final daysSinceLastRequest =
        now.difference(_lastPermissionRequestTime!).inDays;
    _log('距离上次请求权限已过 $daysSinceLastRequest 天');

    if (daysSinceLastRequest >= 1) {
      _log('超过1天，需要重新请求权限');
      return true;
    }

    // 对于华为设备，额外检查权限状态
    if (Platform.isAndroid && await _isHuaweiDevice()) {
      _log('华为设备，检查当前权限状态');
      final manageStatus = await Permission.manageExternalStorage.status;
      final storageStatus = await Permission.storage.status;

      _log('华为设备权限状态 - 外部存储: $manageStatus, 标准存储: $storageStatus');

      // 如果两个权限都没有，需要重新请求
      if (!manageStatus.isGranted && !storageStatus.isGranted) {
        _log('华为设备权限都未授予，需要重新请求');
        return true;
      }
    }

    _log('不需要重新请求权限');
    return false;
  }

  // 检查存储权限
  Future<bool> checkStoragePermission() async {
    try {
      _log('开始检查存储权限');

      // 检查是否需要重新请求权限
      if (await _shouldReRequestPermissions()) {
        _log('需要重新请求权限');
        return await requestAllPermissions();
      }

      // 检查设备信息，特别处理华为设备
      if (Platform.isAndroid) {
        final isHuawei = await _isHuaweiDevice();
        _log('设备制造商检查，是否华为: $isHuawei');

        if (isHuawei) {
          return await _checkHuaweiPermissions();
        } else {
          return await _checkNonHuaweiAndroidPermissions();
        }
      } else if (Platform.isIOS) {
        return await _checkIOSPermissions();
      }

      _log('其他平台，默认返回true');
      return true; // 其他平台默认返回true
    } catch (e) {
      _log('检查存储权限时出错: $e');
      return false;
    }
  }

  // 检查华为设备权限
  Future<bool> _checkHuaweiPermissions() async {
    _log('检查华为设备权限');

    // 华为手机需要特殊的外部存储权限
    final manageExternalStatus = await Permission.manageExternalStorage.status;
    _log('华为手机外部存储权限状态: $manageExternalStatus');

    if (manageExternalStatus.isGranted) {
      _log('华为设备已有外部存储管理权限');
      return true;
    }

    // 检查标准存储权限作为备选
    final storageStatus = await Permission.storage.status;
    _log('华为手机标准存储权限状态: $storageStatus');

    if (storageStatus.isGranted) {
      _log('华为设备已有标准存储权限');
      return true;
    }

    // 如果都没有权限，尝试请求
    _log('华为设备权限不足，尝试请求权限');
    final result = await Permission.manageExternalStorage.request();
    _log('请求外部存储管理权限结果: $result');

    if (result.isGranted) {
      return true;
    }

    // 如果外部存储权限请求失败，尝试标准存储权限
    final storageResult = await Permission.storage.request();
    _log('请求标准存储权限结果: $storageResult');
    return storageResult.isGranted;
  }

  // 检查非华为Android设备权限
  Future<bool> _checkNonHuaweiAndroidPermissions() async {
    _log('检查非华为Android设备权限');

    if (await _isAndroid13OrHigher()) {
      _log('Android 13+设备，检查媒体权限');
      // Android 13+设备检查媒体权限
      final audioStatus = await Permission.audio.status;
      _log('音频权限状态: $audioStatus');

      if (audioStatus.isGranted) {
        _log('已有音频权限');
        return true;
      }

      _log('请求音频权限');
      final result = await Permission.audio.request();
      _log('音频权限请求结果: $result');
      return result.isGranted;
    } else {
      _log('Android 12及以下设备，检查存储权限');
      // 旧版Android检查存储权限
      final storageStatus = await Permission.storage.status;
      _log('存储权限状态: $storageStatus');

      if (storageStatus.isGranted) {
        _log('已有存储权限');
        return true;
      }

      _log('请求存储权限');
      final result = await Permission.storage.request();
      _log('存储权限请求结果: $result');
      return result.isGranted;
    }
  }

  // 检查iOS权限
  Future<bool> _checkIOSPermissions() async {
    _log('检查iOS设备权限');

    // iOS设备检查照片权限
    final photosStatus = await Permission.photos.status;
    _log('iOS照片权限状态: $photosStatus');

    if (photosStatus.isGranted) {
      _log('已有照片权限');
      return true;
    }

    _log('请求照片权限');
    final result = await Permission.photos.request();
    _log('照片权限请求结果: $result');
    return result.isGranted;
  }

  // 判断Android SDK版本是否为13(API 33)或更高，使用 device_info_plus 获取准确版本
  Future<bool> _isAndroid13OrHigher() async {
    if (Platform.isAndroid) {
      try {
        // 使用 device_info_plus 获取准确的 Android 版本信息
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkVersion = androidInfo.version.sdkInt;
        _log('检测到 Android SDK 版本: $sdkVersion');
        return sdkVersion >= 33; // Android 13是API级别33
      } catch (e) {
        _log('获取 Android 版本失败: $e，将假设为 Android 12 或以下');
        return false; // 如果获取失败，假设为旧版本
      }
    }
    return false;
  }

  // 选择单个音频文件
  Future<AudioFileModel?> pickAudioFile() async {
    if (!await checkStoragePermission()) {
      throw Exception('未获得${getPermissionFriendlyName()}');
    }

    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.audio,
      lockParentWindow: true, // 锁定父窗口，减少额外控件
      withData: false, // 不预加载文件数据
      allowCompression: false, // 不允许压缩
    );

    if (result != null && result.files.isNotEmpty) {
      final file = result.files.first;
      if (file.path != null) {
        return _createAudioFileModel(file.path!, 0);
      }
    }

    return null;
  }

  // 选择多个音频文件
  Future<List<AudioFileModel>> pickMultipleAudioFiles() async {
    if (!await checkStoragePermission()) {
      throw Exception('未获得${getPermissionFriendlyName()}');
    }

    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.audio,
      allowMultiple: true,
      lockParentWindow: true, // 锁定父窗口，减少额外控件
      withData: false, // 不预加载文件数据
      allowCompression: false, // 不允许压缩
    );

    if (result != null && result.files.isNotEmpty) {
      List<AudioFileModel> audioFiles = [];
      for (int i = 0; i < result.files.length; i++) {
        final file = result.files[i];
        if (file.path != null) {
          audioFiles.add(_createAudioFileModel(file.path!, i));
        }
      }
      return audioFiles;
    }

    return [];
  }

  // 选择目录并扫描音频文件
  Future<List<AudioFileModel>> pickDirectoryAndScanAudioFiles() async {
    if (!await checkStoragePermission()) {
      throw Exception('未获得${getPermissionFriendlyName()}');
    }

    String? directoryPath = await FilePicker.platform.getDirectoryPath(
      lockParentWindow: true, // 锁定父窗口，减少额外控件
    );

    if (directoryPath != null) {
      return await scanDirectoryForAudioFiles(directoryPath);
    }

    return [];
  }

  // 扫描目录中的音频文件
  Future<List<AudioFileModel>> scanDirectoryForAudioFiles(
      String directoryPath) async {
    final directory = Directory(directoryPath);
    if (!directory.existsSync()) {
      return [];
    }

    List<AudioFileModel> audioFiles = [];
    List<FileSystemEntity> entities = await directory.list().toList();

    // 按文件名排序
    entities
        .sort((a, b) => path.basename(a.path).compareTo(path.basename(b.path)));

    int index = 0;
    for (var entity in entities) {
      if (entity is File) {
        final ext =
            path.extension(entity.path).toLowerCase().replaceAll('.', '');
        if (_supportedAudioFormats.contains(ext)) {
          audioFiles.add(_createAudioFileModel(entity.path, index++));
        }
      }
    }

    return audioFiles;
  }

  // 从文件路径创建AudioFileModel
  AudioFileModel _createAudioFileModel(String filePath, int orderIndex) {
    final fileName = path.basename(filePath);

    // 使用固定默认时长，不进行估算
    int durationInSeconds = 0; // 设置初始时长为0

    // 在后台使用播放器计算精确时长
    _calculateAccurateDuration(filePath);

    return AudioFileModel(
      path: filePath,
      fileName: fileName,
      durationInSeconds: durationInSeconds, // 不使用默认值，让UI显示加载中状态
      orderIndex: orderIndex,
    );
  }

  // 使用播放器在后台计算精确时长
  void _calculateAccurateDuration(String filePath) {
    _log('【添加文件时】开始计算音频文件精确时长: $filePath');

    // 不使用microtask，而是直接启动异步任务
    () async {
      try {
        final audioService = AudioService();
        await audioService.init();

        _log('【添加文件时】使用元数据方式获取音频 $filePath 的精确时长（无需播放）...');
        final duration =
            await audioService.calculateAudioFileDuration(filePath);

        if (duration.inSeconds > 0) {
          _log(
              '【添加文件时】计算完成: ${path.basename(filePath)}, 精确时长: ${duration.inSeconds}秒');
          // 时长会在calculateAudioFileDuration方法中自动保存到数据库
        } else {
          _log('【添加文件时】无法获取精确时长');
        }
      } catch (e) {
        _log('【添加文件时】获取精确时长出错: $e');
      }
    }(); // 立即执行
  }

  // 添加一个公共方法用于检查是否为华为设备，供外部调用
  Future<bool> isHuaweiDevice() async {
    return await _isHuaweiDevice();
  }

  // 调试方法：打印当前所有权限状态
  Future<void> debugPrintPermissionStatus() async {
    if (!kDebugMode) return;

    _log('=== 权限状态调试信息 ===');
    _log('平台: ${Platform.operatingSystem}');

    if (Platform.isAndroid) {
      final isHuawei = await _isHuaweiDevice();
      final isAndroid13Plus = await _isAndroid13OrHigher();

      _log('是否华为设备: $isHuawei');
      _log('是否Android 13+: $isAndroid13Plus');

      // 检查各种权限状态
      final storageStatus = await Permission.storage.status;
      final audioStatus = await Permission.audio.status;
      final manageExternalStatus =
          await Permission.manageExternalStorage.status;

      _log('存储权限状态: $storageStatus');
      _log('音频权限状态: $audioStatus');
      _log('外部存储管理权限状态: $manageExternalStatus');
    } else if (Platform.isIOS) {
      final photosStatus = await Permission.photos.status;
      _log('照片权限状态: $photosStatus');
    }

    _log('权限请求历史: $_permissionRequested');
    _log('上次请求时间: $_lastPermissionRequestTime');
    _log('=== 权限状态调试信息结束 ===');
  }
}
