import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter/foundation.dart'; // 导入kDebugMode
import '../models/playlist_model.dart';
import '../services/file_service.dart';
import '../services/audio_service.dart';
import '../database/database_helper.dart';
import '../widgets/audio_tile.dart';
import '../utils/app_theme.dart';

// 添加日志函数，在非调试模式下不输出
void _log(String message) {
  if (kDebugMode) {
    print(message);
  }
}

class PlaylistEditScreen extends StatefulWidget {
  final PlaylistModel? playlist;

  const PlaylistEditScreen({Key? key, this.playlist}) : super(key: key);

  @override
  State<PlaylistEditScreen> createState() => _PlaylistEditScreenState();
}

class _PlaylistEditScreenState extends State<PlaylistEditScreen> {
  final TextEditingController _nameController = TextEditingController();
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  final FileService _fileService = FileService();

  List<AudioFileModel> _audioFiles = [];
  bool _isLoading = false;
  bool _isNameValid = true;

  @override
  void initState() {
    super.initState();

    // 确保数据库已初始化
    _initDatabase();

    // 订阅音频时长变更事件
    _subscribeToDurationChanges();
  }

  // 初始化数据库并加载播放列表
  Future<void> _initDatabase() async {
    try {
      // 确保数据库已加载
      await _databaseHelper.database;

      if (widget.playlist != null) {
        setState(() {
          _nameController.text = widget.playlist!.name;
          _audioFiles = List.from(widget.playlist!.audioFiles);
        });

        // 在数据库准备好后刷新所有文件的时长信息
        await _refreshAllFileDurations();
      }
    } catch (e) {
      _log('初始化数据库失败: $e');
    }
  }

  Future<void> _refreshAllFileDurations() async {
    if (_audioFiles.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      // 初始化AudioService
      final audioService = AudioService();
      await audioService.init();

      final updatedAudioFiles = <AudioFileModel>[];

      for (var audioFile in _audioFiles) {
        // 从数据库获取时长，但不再计算新的时长
        int duration =
            await _databaseHelper.getAudioFileDuration(audioFile.path);

        if (duration > 0 && duration != audioFile.durationInSeconds) {
          _log(
              '刷新文件时长: ${audioFile.fileName}, 旧: ${audioFile.durationInSeconds}秒, 新: $duration秒');

          // 使用AudioService更新时长，确保在所有播放列表中同步
          await audioService.updateAndStoreAudioDuration(
              audioFile.path, Duration(seconds: duration));

          updatedAudioFiles
              .add(audioFile.copyWith(durationInSeconds: duration));
        } else {
          updatedAudioFiles.add(audioFile);
          // 不再计算精确时长，只使用数据库中的现有数据
        }
      }

      if (mounted) {
        setState(() {
          _audioFiles = updatedAudioFiles;
        });
      }
    } catch (e) {
      _log('刷新文件时长失败: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _pickAudioFiles() async {
    setState(() => _isLoading = true);

    try {
      final audioFiles = await _fileService.pickMultipleAudioFiles();

      if (audioFiles.isNotEmpty) {
        setState(() {
          int maxOrderIndex = _audioFiles.isEmpty
              ? -1
              : _audioFiles
                  .map((e) => e.orderIndex)
                  .reduce((a, b) => a > b ? a : b);

          for (int i = 0; i < audioFiles.length; i++) {
            audioFiles[i] =
                audioFiles[i].copyWith(orderIndex: maxOrderIndex + i + 1);

            if (!_audioFiles.any((file) => file.path == audioFiles[i].path)) {
              _audioFiles.add(audioFiles[i]);
            }
          }

          _updateOrderIndices();
        });

        // FileService 已经在后台计算时长，这里不需要再计算
        _log('文件已添加，时长将由 FileService 在后台计算');

        setState(() => _isLoading = false);
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择文件失败: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  // 订阅音频时长变更事件
  void _subscribeToDurationChanges() {
    final audioService = AudioService();
    audioService.audioEventBus.on<AudioEvent>().listen((event) {
      if (event.eventType == AudioEventType.durationChanged && mounted) {
        _log(
            '收到音频时长变更事件: ${event.audioFilePath}, 新时长: ${event.duration?.inSeconds}秒');
        // 收到事件后更新UI
        if (event.audioFilePath != null &&
            event.duration != null &&
            event.duration!.inSeconds > 0) {
          _updateAudioFileDurationInUI(
              event.audioFilePath!, event.duration!.inSeconds);
        }
      }
    });
  }

  // 直接在UI中更新音频文件时长
  void _updateAudioFileDurationInUI(String filePath, int durationInSeconds) {
    if (!mounted) return;

    final fileName = filePath.split('/').last.split('\\').last;
    bool found = false;
    AudioFileModel? updatedFile;

    // 查找并更新匹配的文件
    for (int i = 0; i < _audioFiles.length; i++) {
      if (_audioFiles[i].path == filePath) {
        found = true;
        if (_audioFiles[i].durationInSeconds != durationInSeconds) {
          _log(
              'UI更新时长: $fileName 从 ${_audioFiles[i].durationInSeconds}秒 变为 $durationInSeconds秒');
          updatedFile =
              _audioFiles[i].copyWith(durationInSeconds: durationInSeconds);
        } else {
          _log('文件时长无变化: $fileName 保持 $durationInSeconds 秒');
        }
        break;
      }
    }

    // 找到文件并需要更新
    if (found && updatedFile != null) {
      setState(() {
        // 更新匹配的文件
        for (int i = 0; i < _audioFiles.length; i++) {
          if (_audioFiles[i].path == filePath) {
            _audioFiles[i] = updatedFile!;
            break;
          }
        }
      });
      _log('UI已通过事件更新: $fileName 时长已设置为 $durationInSeconds 秒');
    }
  }

  // 刷新单个文件的时长并更新UI
  Future<void> _refreshSingleFileDuration(String filePath) async {
    final fileName = filePath.split('/').last.split('\\').last;
    _log('开始刷新文件时长: $fileName');

    try {
      // 从数据库获取最新时长
      int duration = await _databaseHelper.getAudioFileDuration(filePath);

      _log('数据库返回时长: $fileName => $duration 秒');

      // 直接使用UI更新方法
      if (duration > 0) {
        _updateAudioFileDurationInUI(filePath, duration);
      } else {
        _log('数据库中没有文件 $fileName 的有效时长记录，将使用默认值');
      }
    } catch (e) {
      _log('刷新单个文件时长失败: $e');
    }
  }

  Future<void> _pickDirectory() async {
    setState(() => _isLoading = true);

    try {
      final audioFiles = await _fileService.pickDirectoryAndScanAudioFiles();

      if (audioFiles.isNotEmpty) {
        setState(() {
          int maxOrderIndex = _audioFiles.isEmpty
              ? -1
              : _audioFiles
                  .map((e) => e.orderIndex)
                  .reduce((a, b) => a > b ? a : b);

          for (int i = 0; i < audioFiles.length; i++) {
            audioFiles[i] =
                audioFiles[i].copyWith(orderIndex: maxOrderIndex + i + 1);

            if (!_audioFiles.any((file) => file.path == audioFiles[i].path)) {
              _audioFiles.add(audioFiles[i]);
            }
          }

          _updateOrderIndices();
        });

        // FileService 已经在后台计算时长，这里不需要再计算
        _log('文件夹已扫描，文件已添加，时长将由 FileService 在后台计算');

        setState(() => _isLoading = false);
      } else {
        setState(() => _isLoading = false);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('扫描目录失败: $e')),
      );
      setState(() => _isLoading = false);
    }
  }

  void _updateOrderIndices() {
    for (int i = 0; i < _audioFiles.length; i++) {
      _audioFiles[i] = _audioFiles[i].copyWith(orderIndex: i);
    }
  }

  void _removeAudioFile(int index) {
    setState(() {
      _audioFiles.removeAt(index);
      _updateOrderIndices();
    });
  }

  void _reorderAudioFiles(int oldIndex, int newIndex) {
    setState(() {
      if (oldIndex < newIndex) {
        newIndex -= 1;
      }
      final item = _audioFiles.removeAt(oldIndex);
      _audioFiles.insert(newIndex, item);
      _updateOrderIndices();
    });
  }

  bool _validatePlaylist() {
    final name = _nameController.text.trim();
    final isNameValid = name.isNotEmpty;

    setState(() {
      _isNameValid = isNameValid;
    });

    return isNameValid && _audioFiles.isNotEmpty;
  }

  Future<void> _savePlaylist() async {
    if (!_validatePlaylist()) {
      if (_audioFiles.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请添加至少一个音频文件')),
        );
      }
      return;
    }

    setState(() => _isLoading = true);

    try {
      // 去除重复文件，只保留相同路径的第一个文件
      final Map<String, AudioFileModel> uniqueFiles = {};
      int duplicatesRemoved = 0;

      for (final file in _audioFiles) {
        if (!uniqueFiles.containsKey(file.path)) {
          uniqueFiles[file.path] = file;
        } else {
          duplicatesRemoved++;
        }
      }

      if (duplicatesRemoved > 0) {
        setState(() {
          _audioFiles = uniqueFiles.values.toList();
        });

        // 显示删除重复文件的提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已删除 $duplicatesRemoved 个重复文件')),
        );
      }

      // 按文件名排序
      _audioFiles.sort((a, b) => a.fileName.compareTo(b.fileName));
      _updateOrderIndices();

      // 显示排序提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('已按文件名排序')),
      );

      // 只从数据库读取现有时长，不做新的计算
      _log('保存前从数据库获取音频文件的时长');

      final updatedAudioFiles = <AudioFileModel>[];

      // 逐个查询数据库中的最新时长
      for (var audioFile in _audioFiles) {
        int duration =
            await _databaseHelper.getAudioFileDuration(audioFile.path);
        if (duration > 0 && duration != audioFile.durationInSeconds) {
          _log(
              '更新保存前的时长: ${audioFile.fileName}, 从 ${audioFile.durationInSeconds}秒 到 ${duration}秒');
          updatedAudioFiles
              .add(audioFile.copyWith(durationInSeconds: duration));
        } else {
          updatedAudioFiles.add(audioFile);
        }
      }

      // 更新内存中的列表
      if (mounted) {
        setState(() {
          _audioFiles = updatedAudioFiles;
        });
      }

      final name = _nameController.text.trim();
      final now = DateTime.now();

      final playlistToSave = PlaylistModel(
        id: widget.playlist?.id,
        name: name,
        audioFiles: _audioFiles,
        createdAt: widget.playlist?.createdAt ?? now,
        updatedAt: now,
      );

      if (widget.playlist?.id != null) {
        await _databaseHelper.updatePlaylist(playlistToSave);
      } else {
        await _databaseHelper.insertPlaylist(playlistToSave);
      }

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存播放列表失败: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.playlist != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? '编辑播放列表' : '新建播放列表'),
        actions: [
          IconButton(
            onPressed: _savePlaylist,
            icon: const Icon(Icons.save),
            tooltip: '保存',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: '播放列表名称',
                      hintText: '输入播放列表名称',
                      errorText: _isNameValid ? null : '请输入播放列表名称',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.playlist_play),
                    ),
                    textInputAction: TextInputAction.done,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _pickAudioFiles,
                          icon: const Icon(Icons.add),
                          label: const Text('添加文件'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.all(12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _pickDirectory,
                          icon: const Icon(Icons.folder),
                          label: const Text('选择文件夹'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.all(12),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline,
                          size: 16, color: AppTheme.textSecondaryColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '长按并拖动可重新排序音频文件',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 24),
                Expanded(
                  child: _audioFiles.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.audio_file,
                                size: 64,
                                color: Colors.grey,
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                '暂无音频文件',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 8),
                              const Text(
                                '点击上方按钮添加音频文件',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                      : ReorderableListView.builder(
                          itemCount: _audioFiles.length,
                          onReorder: _reorderAudioFiles,
                          itemBuilder: (context, index) {
                            final audioFile = _audioFiles[index];
                            return AudioTile(
                              key: ValueKey(audioFile.path),
                              audioFile: audioFile,
                              onTap: () {},
                              onDelete: () => _removeAudioFile(index),
                              disableSystemPlayer: true,
                              hidePlayButton: true,
                              onDurationLoaded: (path, newDuration) {
                                // 检测到AudioTile加载到了新的时长，更新列表
                                _log('AudioTile回调更新时长: $path, $newDuration 秒');
                                if (mounted) {
                                  setState(() {
                                    _audioFiles[index] = audioFile.copyWith(
                                      durationInSeconds: newDuration,
                                    );
                                  });
                                }
                              },
                            );
                          },
                        ),
                ),
              ],
            ),
      bottomNavigationBar: _audioFiles.isNotEmpty
          ? SafeArea(
              child: Container(
                padding: const EdgeInsets.all(16),
                color: Colors.white,
                child: Row(
                  children: [
                    Text(
                      '共 ${_audioFiles.length} 个文件',
                      style: TextStyle(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const Spacer(),
                    ElevatedButton(
                      onPressed: _savePlaylist,
                      child: Text(isEditing ? '更新播放列表' : '保存播放列表'),
                    ),
                  ],
                ),
              ),
            )
          : null,
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
}
